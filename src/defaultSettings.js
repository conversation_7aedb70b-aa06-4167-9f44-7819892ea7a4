/**
 * 项目默认配置项
 * primaryColor - 默认主题色
 * navTheme - sidebar theme ['dark', 'light'] 两种主题
 * colorWeak - 色盲模式
 * layout - 整体布局方式 ['sidemenu', 'topmenu'] 两种布局
 * fixedHeader - 固定 Header : boolean
 * fixSiderbar - 固定左侧菜单栏 ： boolean
 * autoHideHeader - 向下滚动时，隐藏 Header : boolean
 * contentWidth - 内容区布局： 流式 |  固定
 *
 * storageOptions: {} - Vue-ls 插件配置项 (localStorage/sessionStorage)
 *
 */

export default {
  primaryColor: '#1890FF', // primary color of ant design
  navTheme: 'dark', // theme for nav menu
  layout: 'sidemenu', // nav menu position: sidemenu or topmenu
  contentWidth: 'Fixed', // layout of content: Fluid or Fixed, only works when layout is topmenu
  fixedHeader: true, // sticky header
  fixSiderbar: true, // sticky siderbar
  autoHideHeader: false, //  auto hide header
  colorWeak: false,
  multipage: true, //默认多页签模式
  // vue-ls options
  storageOptions: {
    namespace: 'pro__', // key prefix
    name: 'ls', // name variable Vue.[ls] or this.[$ls],
    storage: 'local', // storage name session, local, memory
  },
  menu:{
    topMenuNumber: {
      0: ['9502685863ab87f0ad1134142788a385', '1424921183907356674'], // 首页
      1: ['1272365174292037633', '1275268593105256449', '1375362055094202369', '1559443730124054529', '1686294856750592002'], // '数据源管理'||'配置管理' 分析配置
      2: ['1272367644980363265', '1272367806670782465', '1272368035319070722', '1272372434506432514', '1573191800244731905', '1395271673115090946', '1715269273428013057'], // '标签管理' || '事实标签' || '模型标签' || '基础规则' || '标签统计'
      3: ['1272371891822215170', '1356589187191128065', '1399653814573776897', '1716995952468545538'],// 用户
      4: ['1584746938260951041', '1676785133348503554', '1925159656631607298', '1925389887238942722'], // 高级分析
      5: ['1513349012965625858', '1516954650579075074', '1516955366269943810', '1516955524621697025'], // 实时数据
      6: ['1595703564802789377', '1272372897716977665', '1272373288961654785'],// 输出管理   输出统计
      7: ['d7d6e2e4e2934f2c9385a623fd98c6f3'],// 操作
    },
    topMenuNames: {
      0: '首页',
      1: '数据源',
      2: '标签',
      3: '人群',
      4: '高级分析',
      5: '实时数据',
      6: '文件',// '输出',
      7: '系统操作'
    }
  }
}