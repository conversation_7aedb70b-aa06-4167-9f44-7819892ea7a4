<template>
  <header class="sd-header" :disabled="disabled" :style="headerStyle">
    <div class="sd-header-bg" :style="{width: titleWidth}">
      <span class="sd-header-sn">{{titleSn|| '01'}}</span>
      <span class="sd-header-title">{{title}}</span>
    </div>
    <slot></slot>
  </header>
</template>
<script>
  export default {
    name: 'ItemHeader',
    props: {
      titleSn: {
        type: String,
        default: null
      },
      title: {
        type: String,
        default: null
      },
      disabled: {
        type: Boolean,
        default: false
      },
      headerStyle: {
        type: Object,
        default: () => {
          return {};
        }
      },
      titleWidth: {
        type: String,
        default: '150px'
      }
    },
  }
</script>
<style lang="less" scoped>
  .sd-header {
    position: relative;
    height: 32px;
    width: 100%;
    background: var(--fade8);
    border-radius: 32px 0 0 32px;
    margin-bottom: 10px;
    font-weight: bold;
  }

  .sd-header-bg {
    height: 32px;
    width: 110px;
    /*background: var(--tint20);*/
    border-radius: 32px 0 0 32px;
    border-bottom: 2px solid var(--tint20);
    /*color: white;*/
    position: relative;
  }

  .sd-header-sn {
    background-color: white;
    border-radius: 50%;
    height: 28px;
    display: inline-block;
    width: 28px;
    text-align: center;
    line-height: 28px;
    /*margin: 2px;*/
    color: var(--tint20);
  }

  .sd-header-title {
    margin-left: 10px;
  }

  .sd-header[disabled] {
    background: #eaeaea;

    .sd-header-bg {
      color: #cdcdcd;
      border-bottom: none;
    }

    .sd-header-sn {
      color: #cdcdcd;
    }
  }
</style>