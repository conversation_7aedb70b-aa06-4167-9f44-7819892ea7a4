<template>
  <div style="padding: 10px 10px 10px 10px;background-color: #F0F2F5;">
    <!--<div slot="title">
      <span class="ivu-pl-8" style="font-weight:bold;font-size:15px;">标签效果分析</span>
    </div>-->
    <Row style="padding: 12px 12px 0;">
   <!-- <a-spin size="large" :spinning="loading" tip="数据加载中，请稍等......">-->
      <card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <item-header title="系统标签调用情况">
          <div class="aa-cs-wrap">
            <date-extend-range-picker ref="dateExtendRangePicker" class="mr12" v-model="analysisParams"
                                      :editable="false" type="range" :shortcuts="analysisShortCuts"
                                      :width="analysisDateWidth" hide-tab="relative_range"
                                      :disabled-date="analysisDateOptions.disabledDate"
                                      @input="changeAnalysisRangeDate"/>
            <Button class="mr12" icon="md-search" type="primary" size="small" @click="loadChartAndTable">查询
            </Button>
          </div>
        </item-header>
        <div ref="myPie" align="center" :style="{height: '385px'}"/>
      </card>
    </Row>
    <row style="padding: 12px 12px 0;">
      <card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
          <item-header title-sn="02" title="TOP3">
            <div class="aa-cs-wrap">
              <Button class="mr12" icon="md-download" type="primary" size="small" @click="">下载</Button>
            </div>
          </item-header>
        <Row>
          <Col :span="8" style="padding-right: 12px;">
            <div ref="myLine1" align="center" :style="{height: '350px'}"/>
          </Col>
          <Col :span="8" style="padding-right: 12px;">
            <div ref="myLine2" align="center" :style="{height: '350px'}"/>
          </Col>
          <Col :span="8" style="padding-right: 12px;">
            <div ref="myLine3" align="center" :style="{height: '350px'}"/>
          </Col>
        </Row>
      </card>
    </row>
    <row style="padding: 12px 12px 0;">
      <card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <item-header title-sn="03" style="display: flex; align-items: center; justify-content: space-between;" title="标签效果详情">
          <div class="query-condition-wrap">
            <!-- 标签检索：文本输入框 -->
            <div class="query-item" style="flex: 1 1 100px; min-width: 100px;">
              <Input v-model="labelInfo" placeholder="请输入标签信息" class="mr12" style="width: 150px;" />
            </div>

            <!-- 使用平台：下拉选择 -->
            <div class="query-item" style="flex: 1 0 100px; min-width: 100px;"> <!-- 固定宽度，不压缩 -->
              <a-select v-model="appCode" placeholder="请选择使用平台" style="width: 150px;">
                <a-select-option value="NDMS">桌面管理</a-select-option>
                <a-select-option value="NVAS">增值交易</a-select-option>
                <a-select-option value="CMS">CMS系统</a-select-option>
              </a-select>
            </div>

            <!-- 具体位置：下拉选择 -->
            <div class="query-item" style="flex: 1 0 100px; min-width: 100px;"> <!-- 固定宽度，不压缩 -->
              <a-select v-model="itemType" placeholder="请选择具体位置" style="width: 150px;">
                <a-select-option value="epg_group">epg分组</a-select-option>
              </a-select>
            </div>

            <!-- 下载按钮 -->
            <div class="query-btn" style="flex-shrink: 0;"> <!-- 按钮不压缩 -->
              <Button class="mr12" icon="md-search" type="primary" size="small" @click="queryTable">查询</Button>
            </div>

            <!-- 下载按钮 -->
            <div class="download-btn" style="flex-shrink: 0;"> <!-- 按钮不压缩 -->
              <Button class="mr12" icon="md-download" type="primary" size="small" @click="">下载Excel</Button>
            </div>
          </div>
        </item-header>
        <a-empty :image="simpleImage" description="暂无数据" v-if="tableData.length <= 0"/>
        <div v-if="tableData.length > 0" style="width: 100%;padding: 10px">
          <template>
            <Table border
                   :columns="columns"
                   :data="tableData"
                   :loading="tableLoading"
            >
              <!--<template slot="labelType">{{labelType}}
                <div v-if="labelType === 3">模型标签</div>
                <div v-if="labelType === 2">事实标签</div>
              </template>-->
              <template slot-scope="{ row }" slot="labelType">
                <Tag v-if="row.labelType === 3">模型标签</Tag>
                <Tag v-if="row.labelType === 2">事实标签</Tag>
              </template>
              <template slot-scope="{ row }" slot="onlineStatus">
                <Tag color="green" v-if="row.onlineStatus === 'online'">上线</Tag>
                <Tag color="red" v-if="row.onlineStatus === 'offline'">下线</Tag>
              </template>
              <template slot-scope="{ row }" slot="bindStatus">
                <Tag color="green" v-if="row.bindStatus === 'bind'">绑定</Tag>
                <Tag color="red" v-if="row.bindStatus === 'unbind'">解绑</Tag>
              </template>
              <template slot-scope="{ row, index }" slot="action">
                <template >
                  <Button type="primary" ghost
                          icon="md-eye" @click="viewHistory(row)">详情
                  </Button>
                  <Button  type="primary" ghost
                           icon="md-eye" style="margin-left:10px;" @click="viewHistory(row)">查看群体分析
                  </Button>
                </template>
              </template>
          </Table>
            <a-drawer title="数据权限规则"
                      :width="drawerWidth"
                      @close="onClose"
                      :visible="drawerLoading"
                      :wrapStyle="{overflow: 'auto'}">
              <p>Some contents...</p>
              <p>Some contents...</p>
              <p>Some contents...</p>
            </a-drawer>
          </template>
          <Row type="flex" justify="end" style="padding-top: 10px">
            <Page
              :current="paginationConfig.current"
              :total="paginationConfig.total"
              :page-size="paginationConfig.pageSize"
              @on-change="handlePageChange"
              @on-page-size-change="handlePageSizeChange"
              :page-size-opts="[10, 20, 50, 100]"
              size="small"
              show-total
              show-elevator
              show-sizer
            ></Page>
          </Row>
        </div>
      </card>
    </row>
  </div>
</template>

<script>

  import {
    Modal,
    ButtonGroup, Button,
    Card,
    Input, Icon,
    Layout,
    Row, Col,
    Sider,
    Split,
    Table,
    Page,
    Tabs,
    TabPane,
    Poptip,Breadcrumb, BreadcrumbItem, Tooltip
  } from 'view-design';
  import echarts from 'echarts'
  import {getAction} from '@/api/manage'
  import ItemHeader from "../main-components/itemCard/ItemHeader";
  import moment from 'moment';
  import AdvancedAnalysisTargetParent from "./analysis/AdvancedAnalysisTargetParent";
  import {Empty} from 'ant-design-vue';
  import DateExtendRangePicker from "../../libs/iview-pro/components/date-extend-picker/date-extend-range-picker";

  import UgsSpin from "../modules/spin/UgsSpin";

  export default {
    name: "LabelOperateAnalyse",
    components: {
      UgsSpin,
      DateExtendRangePicker,
      AdvancedAnalysisTargetParent,
      ItemHeader,
      Tooltip,
      Input, Icon,
      ButtonGroup, Button, Modal,
      Table,
      Layout,
      Split,
      Card,
      Sider,
      Tabs,TabPane,
      Row, Col,
      Page,
    },
    data() {
      const nowMil = Date.now();
      let dayMil = 3600 * 1000 * 24;
      const daysRangeEnd = new Date(nowMil);
      const daysRangeStart = new Date();
      daysRangeStart.setTime(nowMil - dayMil * 14);
      let dayRanges = this.setDaysRange(daysRangeStart, daysRangeEnd);
      const todayInt = parseInt(moment().format('YYYYMMDD'));

      return {
        labelInfo:'',
        appCode:'',
        itemType:'',
        pieAppCode: '',
        drawerWidth: 650,
        drawerLoading: false,
        simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
        analysisParams: {
          type: 'past_15_days', from: 'cut',
        },
        analysisDateParams: [daysRangeStart, daysRangeEnd],
        analysisDateWidth: 290,
        analysisShortCuts: [
          {
            text: '过去15天',
            code: "past_15_days",
            value() {
              const end = new Date();
              const start = new Date();
              start.setDate(end.getDate() - 14); // 从今天开始往前推14天，因为包括今天在内总共是15天
              return [start, end];
            }
          },
          {
            text: '本月',
            code: "this_month",
            value() {
              const end = new Date();
              const start = new Date();
              start.setDate(1);
              return [start, end];
            }
          },
          {
            text: '上月',
            code: "last_month",
            value() {
              const end = new Date();
              end.setDate(1);
              end.setTime(end.getTime() - dayMil);
              const start = new Date(end.getTime());
              start.setDate(1);
              return [start, end];
            }
          },
          {
            text: '过去3个月',
            code: "past_3_months",
            value() {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              return [start, end];
            }
          },
          {
            text: '过去6个月',
            code: "past_6_months",
            value() {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 6);
              return [start, end];
            }
          },
          {
            text: '过去1年',
            code: "past_1_year",
            value() {
              const end = new Date();
              const start = new Date();
              start.setFullYear(start.getFullYear() - 1);
              return [start, end];
            }
          },
          {
            text: '本年',
            code: "this_year",
            value() {
              const end = new Date();
              const start = new Date();
              start.setMonth(0);
              start.setDate(1);
              return [start, end];
            }
          },
          {
            text: '去年',
            code: "last_year",
            value() {
              const now = new Date();
              const year = now.getFullYear() - 1;
              const start = new Date(year, 0, 1);
              const end = new Date(year, 11, 31);
              return [start, end];
            }
          }

        ],
        analysisDateOptions: {
          disabledDate(dateInt) {
            return  dateInt > todayInt;
          }
        },
        daysRange: dayRanges,
        vsDaysRange: [],
        loading: true,
        // 过去1天、过去30天、过去90天
        dateType: '2', // 前一天的传值1、前一个月的传2、前三个月的传3
        url: {
          countReportStatistics: "/label/operate/report/statistics",
          countTopStatistics: "/label/operate/top/statistics",
          queryTablePage: "/label/operate/page",
        },
        reportResult: {},
        // symbolSize 点的大小
        data: [
          [[0.0262, 0.0367, 31070, "哈尔滨离网用户", 2021], [0.4017, 0, 7598, "黑龙江激活观影未消费用户", 2021], [0.6802, 0.2205, 2705, "未来30天到期用户", 2021], [0.0052, 0.0036, 66500, "哈尔滨销户用户", 2021], [0, 0, 329, "伊春观影不活跃用户", 2021], [0.7833, 0.0452, 3475, "喜羊羊观影", 2021], [0.7225, 0.0438, 2310, "无间道观影用户", 2021], [0.5295, 0.0167, 272013, "上周活跃", 2021], [0.026, 0.0127, 177526, "上周不活跃", 2021], [0.4394, 0.0132, 329059, "上月活跃", 2021], [0.0337, 0.0203, 120476, "上月不活跃", 2021], [0.3432, 0.0375, 1842572, "电视剧", 2021], [0.4076, 0.0387, 1065591, "曲艺", 2021], [0.4504, 0.0417, 833817, "生活", 2021], [0.3834, 0.0406, 1052510, "电影", 2021], [0.3736, 0.0329, 1608429, "综艺", 2021], [0.6082, 0.0401, 175454, "科教", 2021], [0.4168, 0.0161, 20231, "电竞", 2021], [0.4121, 0.0131, 193337, "体育", 2021], [0.4571, 0.0118, 214311, "纪录片", 2021], [0.4222, 0.0378, 1009510, "香港", 2021], [0.3779, 0.0358, 1343721, "美国", 2021], [0.4569, 0.0422, 513264, "韩国", 2021], [0.38, 0.0241, 1161922, "中国内地", 2021], [0.2323, 0.0091, 21590, "台湾", 2021], [0.3591, 0.0072, 17884, "越南", 2021], [0.5094, 0.0282, 966112, "凌晨", 2021], [0.5249, 0.035, 1074840, "早晨", 2021], [0.4822, 0.0363, 1279518, "上午", 2021], [0.4684, 0.0354, 1347352, "下午", 2021], [0.4582, 0.033, 1386258, "晚上", 2021], [0.4776, 0.024, 1234605, "深夜", 2021], [0.3902, 0.0202, 162769, "传记", 2021], [0.3949, 0.0134, 27405, "正剧", 2021], [0.4435, 0.0133, 43895, "纪实", 2021], [0.4304, 0.0184, 27293, "历史古装", 2021], [0.319, 0.019, 17650, "歌舞", 2021], [0.415, 0.022, 733957, "历史", 2021], [0.2859, 0.0115, 190732, "动作", 2021], [0.3341, 0.0153, 159898, "成长", 2021], [0.4315, 0.0158, 316850, "军事", 2021], [0.373, 0.0232, 1145581, "武侠", 2021], [0.3732, 0.0233, 901666, "青春", 2021], [0.3728, 0.025, 749732, "科幻", 2021], [0.3914, 0.023, 1009766, "悬疑", 2021], [0.3515, 0.0116, 27254, "刺激", 2021], [0.2815, 0.0115, 7769, "治愈", 2021], [0.243, 0.0013, 9269, "黑色幽默", 2021], [0.4259, 0.0181, 9249, "亲子", 2021], [0.3462, 0.0089, 11782, "职场", 2021], [0.3593, 0.0237, 242543, "游戏", 2021], [0.4207, 0.0107, 5220, "乡村", 2021], [0.3493, 0.0027, 3710, "家庭", 2021], [0.3713, 0.0045, 72382, "社会", 2021], [0.5305, 0.0121, 79294, "经济", 2021], [0.2562, 0.0087, 28705, "民生", 2021], [0.5458, 0.0214, 347077, "哈尔滨活跃", 2021], [0.0221, 0.036, 25562, "哈尔滨不活跃", 2021], [0.1782, 0.0141, 1468369, "哈尔滨", 2021], [0.4645, 0.0127, 386713, "哈尔滨夜猫子用户", 2021], [0.4068, 0, 118, "哈尔滨姥爷", 2021], [0.2881, 0, 427, "哈尔滨姐姐测试", 2021], [0, 0, 0, "测试", 2021], [0.5027, 0.4816, 27261, "哈尔滨最近一个月订购用户", 2021], [0.4794, 0, 3767, "鹤岗是未订购用户30天内活跃", 2021], [0.5848, 0.5384, 2996, "鹤岗近一个月的订购用户数", 2021], [0.6764, 0.4809, 8010, "齐齐哈尔近一个月有订购的用户", 2021], [0, 0, 4, "测试", 2021], [0.8331, 0.037, 10602, "乡村爱情观影", 2021], [0.3844, 0, 7940, "新用户", 2021], [0.6332, 0.0303, 131830, "历史支付用户", 2021], [0.7061, 0.0264, 40924, "运营测试", 2021], [0.7652, 0.0298, 820262, "活跃用户", 2021], [0, 0.0007, 346967, "沉默用户", 2021], [0.6581, 0, 885445, "高活未付费", 2021], [0.686, 0.0321, 108357, "节目偏好-你是我的城池营垒", 2021], [0.2027, 0.0138, 299205, "牡丹江家客用户", 2021], [0, 0, 0, "哈尔滨电竞免费用户0407", 2021], [0.4823, 0.4598, 26344, "哈尔滨消费超过10元", 2021], [0.671, 0.3559, 12285, "哈尔滨消费大于20元", 2021], [0.7042, 0.5911, 4997, "哈尔滨消费大于30元", 2021], [0.5316, 0.3642, 36046, "哈尔滨消费用户", 2021], [0.4774, 0.467, 25722, "哈尔滨消费大于15", 2021]]
        ],
        activityData: [],
        arpuData: [],
        countData: [],
        chart: null,
        displayDataZoom: true,
        allUserCount: 0,
        tableLoading: false,
        tableData : [],
        columns: [
          {
            title: '标签名称',
            key: 'labelName',
            align: 'center'
          },
          {
            title: '标签创建时间',
            key: 'labelCreateTime',
            align: 'center',
            // sortable: true
          },
          {
            title: '标签属性',
            key: 'labelType',
            align: 'center',
            // scopedSlots: {customRender: 'labelType'},
            slot: 'labelType'
            // sortable: true
          },
          {
            title: '关联用户数',
            key: 'labelBindCount',
            align: 'center',
            // sortable: true
          },
          {
            title: '最后一次运算时间',
            key: 'operationDate',
            align: 'center',
            // sortable: true
          },
          {
            title: '运营平台',
            key: 'appName',
            align: 'center',
            // sortable: true
          },
          {
            title: '具体位置',
            key: 'itemName',
            align: 'center',
            // sortable: true
          },
          {
            title: '绑定状态',
            key: 'bindStatus',
            align: 'center',
            // sortable: true
            scopedSlots: {customRender: 'bindStatus'},
            slot: 'bindStatus'
          },
          {
            title: '绑定/解绑时间',
            key: 'bindDate',
            align: 'center',
            // sortable: true
          },
          {
            title: '上线状态',
            key: 'onlineStatus',
            align: 'center',
            scopedSlots: {customRender: 'onlineStatus'},
            slot: 'onlineStatus'
            // sortable: true
          },
          {
            title: '上线/下线时间',
            key: 'onlineDate',
            align: 'center',
            // sortable: true
          },
          {
            title: '操作',
            scopedSlots: {customRender: 'action'},
            key: 'action',
            width: 280,
            align: 'center',
            fixed: 'right',
            slot: 'action'
          }
        ],
        paginationConfig: {
          current: 1,
          pageSize: 10,
          total: 0,
          pageSizeOptions: [10, 20, 30],
          showTotal: (total) => `总共 ${total} 条数据`
        }
      };
    },
    mounted() {
      //this.loadAllUserCount();
      this.initData();
    },
    created() {
      this.resetScreenSize()
    },
    computed: {
    },
    methods: {
      onClose() {
        this.drawerLoading = false
      },
      // 根据屏幕变化,设置抽屉尺寸
      resetScreenSize() {
        let screenWidth = document.body.clientWidth
        if (screenWidth < 500) {
          this.drawerWidth = screenWidth
        } else {
          this.drawerWidth = 650
        }
      },
      changeAnalysisDateWidth(name) {
        this.analysisDateWidth = name ? 290 + 270 : 290;
      },
      changeAnalysisRangeDate(v1) {
        if (v1.value && v1.value.length === 2) {
          this.analysisDateParams = v1.value;
        } else if (v1.from === "shortcut") {
          for (let i = 0; i < this.analysisShortCuts.length; i++) {
            let cut = this.analysisShortCuts[i];
            if (cut.code === v1.type) {
              this.analysisDateParams = [...cut.value()];
              break;
            }
          }
        }
      },
      setDaysRange(before, tail) {
        let start = moment(before), end = moment(tail);
        let tmp = [];
        tmp.push(start.format('YYYY-MM-DD'));
        while (start.isBefore(end)) {
          start.add(1, 'day');
          tmp.push(start.format('YYYY-MM-DD'));
        }
        return tmp;
      },
      validatorData() {
        if (!this.$refs.userSearchRuleComp.validatorData()){
          return false;
        }
        return true;
      },
      initData(){
        this.loadPieData();
        this.loadLineChart();
        this.loadTableData();
        this.loading = false;
      },
      loadTableData(){
        this.$nextTick(async () => {
          this.loading = true;
          await getAction(this.url.queryTablePage, {
            labelInfo: this.labelInfo,
            appCode: this.appCode,
            itemType: this.itemType
          }).then((res) => {
            console.log('res', JSON.stringify(res))
            console.log('res.success', res.success)
            if (res && res.success) {
              this.tableData = res.result.records;
            }
          });
        });
      },
      loadLineChart(){
        this.$nextTick(async () => {
          this.loading = true;
          let startTime = (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD')
          let endTime = (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD')
          await getAction(this.url.countTopStatistics,{startDate: startTime, endDate: endTime, appCode: this.pieAppCode}).then((res) => {
            console.log('res', JSON.stringify(res))
            console.log('res.success', res.success)
            if(res && res.success){
              if(res.result.length > 0){
                res.result.forEach((p, index) => {
                  if(index == 0){
                    const data = [];
                    p.data.forEach((item, itemIndex) => {
                      const innerData = [];
                      const key = Object.keys(item)[0];
                      const value = item[key];
                      innerData.push(key, value);
                      data.push(innerData)
                    });
                    console.log('data', JSON.stringify(data))
                    let lineOption1 = {
                      color: ['#063455'], // 全局主色调为蓝色
                      tooltip: {
                        trigger: 'axis',
                        position: function (pt) {
                          return [pt[0], '10%'];
                        }
                      },
                      title: {
                        left: 'center',
                        text: p.labelName + '(' + p.labelCode + ')',
                        subtext: p.appName,
                      },
                      xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        axisLabel: {
                          interval: 0, // 强制显示所有标签
                          rotate: 45,  // 旋转标签避免重叠
                        }
                      },
                      yAxis: {
                        type: 'value',
                        boundaryGap: [0, '100%']
                      },
                      dataZoom: [
                        {
                          type: 'inside',
                          start: 0,
                          end: 100
                        },
                        {
                          type: 'slider',
                          start: 0,
                          end: 100
                        }
                      ],
                      series: [
                        {
                          name: '绑定量',
                          type: 'line',
                          smooth: true,
                          symbol: 'none',
                          areaStyle: {},
                          data: data
                        }
                      ]
                    };
                    var lineDom1 = this.$refs.myLine1;
                    var lineChart1 = echarts.init(lineDom1);
                    lineChart1.clear();
                    lineOption1 && lineChart1.setOption(lineOption1);
                  }else if(index == 1){
                    const data = [];
                    p.data.forEach((item, itemIndex) => {
                      const innerData = [];
                      const key = Object.keys(item)[0];
                      const value = item[key];
                      innerData.push(key, value);
                      data.push(innerData)
                    });
                    console.log('data', JSON.stringify(data))
                    let lineOption2 = {
                      color: ['#063455'], // 全局主色调为蓝色
                      tooltip: {
                        trigger: 'axis',
                        position: function (pt) {
                          return [pt[0], '10%'];
                        }
                      },
                      title: {
                        left: 'center',
                        text: p.labelName + '(' + p.labelCode + ')',
                        subtext: p.appName,
                      },
                      /*toolbox: {
                        feature: {
                          dataZoom: {
                            yAxisIndex: 'none'
                          },
                          restore: {},
                          saveAsImage: {}
                        }
                      },*/
                      xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        axisLabel: {
                          interval: 0, // 强制显示所有标签
                          rotate: 45,  // 旋转标签避免重叠
                        }
                      },
                      yAxis: {
                        type: 'value',
                        boundaryGap: [0, '100%']
                      },
                      dataZoom: [
                        {
                          type: 'inside',
                          start: 0,
                          end: 100
                        },
                        {
                          type: 'slider',
                          start: 0,
                          end: 100
                        }
                      ],
                      series: [
                        {
                          name: '绑定量',
                          type: 'line',
                          smooth: true,
                          symbol: 'none',
                          areaStyle: {},
                          data: data
                        }
                      ]
                    };
                    var lineDom2 = this.$refs.myLine2;
                    var lineChart2 = echarts.init(lineDom2);
                    lineChart2.clear();
                    lineOption2 && lineChart2.setOption(lineOption2);
                  }else if(index == 2){
                    const data = [];
                    p.data.forEach((item, itemIndex) => {
                      const innerData = [];
                      const key = Object.keys(item)[0];
                      const value = item[key];
                      innerData.push(key, value);
                      data.push(innerData)
                    });
                    console.log('data', JSON.stringify(data))
                    let lineOption3 = {
                      color: ['#063455'], // 全局主色调为蓝色
                      tooltip: {
                        trigger: 'axis',
                        position: function (pt) {
                          return [pt[0], '10%'];
                        }
                      },
                      title: {
                        left: 'center',
                        text: p.labelName + '(' + p.labelCode + ')',
                        subtext: p.appName,
                      },
                      /*toolbox: {
                        feature: {
                          dataZoom: {
                            yAxisIndex: 'none'
                          },
                          restore: {},
                          saveAsImage: {}
                        }
                      },*/
                      xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        xAxis: {
                          type: 'time',
                          boundaryGap: false,
                          axisLabel: {
                            interval: 0, // 强制显示所有标签
                            rotate: 45,  // 旋转标签避免重叠
                          }
                        },
                      },
                      yAxis: {
                        type: 'value',
                        boundaryGap: [0, '100%']
                      },
                      dataZoom: [
                        {
                          type: 'inside',
                          start: 0,
                          end: 100
                        },
                        {
                          type: 'slider',
                          start: 0,
                          end: 100
                        }
                      ],
                      series: [
                        {
                          name: '绑定量',
                          type: 'line',
                          smooth: true,
                          symbol: 'none',
                          areaStyle: {},
                          data: data
                        }
                      ]
                    };
                    var lineDom3 = this.$refs.myLine3;
                    var lineChart3 = echarts.init(lineDom3);
                    lineChart3.clear();
                    lineOption3 && lineChart3.setOption(lineOption3);
                  }
                });
              }
            }
          });
        });
      },
      loadPieData(){
        this.$nextTick(async () => {
          this.loading = true;
          let startTime = (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD')
          let endTime = (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD')
          await getAction(this.url.countReportStatistics,{startDate: startTime, endDate: endTime}).then((res) => {
            console.log('res', JSON.stringify(res))
            console.log('res.success', res.success)
            if(res && res.success){
              const data = Object.entries(res.result).map(([name, value]) => ({
                name,
                value
              }));
              console.log('data', JSON.stringify(data))
              let pieOption = {
                /*title: {
                  text: 'Referer of a Website',
                  subtext: 'Fake Data',
                  left: 'center'
                },*/
                tooltip: {
                  trigger: 'item',
                  // 显示百分比
                  formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                  type: 'scroll',  // 当图例项过多时支持滚动
                  orient: 'vertical', // 垂直排列
                  right: '20%', // 调整图例距离右侧的距离，以便给饼图留出空间
                  //top: '10%',  // 将图例的位置向上移动，使得整体布局偏上
                  //left: 'right', // 靠右显示
                  top: 'center',  // 垂直居中
                  align: 'left',  // 文字左对齐
                  padding: [0, 20, 0, 0]  // 右边距留20px
                },
                series: [
                  {
                    name: '系统标签调用情况',
                    type: 'pie',
                    radius: '70%',
                    center: ['40%', '50%'], // 这里调整饼图的中心位置，使其相对于整个容器居中
                    data: data,
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    },
                    // 设置标签展示格式
                    label: {
                      // 设置标签的位置为 inside
                      position: 'inside',
                      // 使用formatter来格式化显示内容，包括百分比
                      formatter: '{d}%',
                      // 根据需要调整字体样式
                      textStyle: {
                        color: '#fff', // 文本颜色，根据背景色选择合适的对比色
                        fontSize: 14 // 字体大小
                      }
                    }
                  }
                ]
              };
              var pieDom = this.$refs.myPie;
              var pieChart = echarts.init(pieDom);
              pieChart.clear();
              pieOption && pieChart.setOption(pieOption);
            }
          });
        });
      },
      doFlushData() {
        this.loading = true;
        this.getAllData(1);
      },
      changeDateType(d, d1) {
        this.dateType = d.target.value;
        this.loading = true;
        this.getAllData();
      },
      onChangeDataZoom(e) {
        if (e) {
          this.displayDataZoom = e.target.checked;
        }
        if (this.chart) {
          delete this.chartOption.dataZoom;
          if (this.displayDataZoom) {
            this.chartOption.dataZoom = [
              {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                start: 0,
                end: 100
              },
              {
                type: 'slider',
                show: true,
                yAxisIndex: [0],
                left: '93%',
                start: 0,
                end: 100
              },
              {
                type: 'inside',
                xAxisIndex: [0],
                start: 0,
                end: 100
              },
              {
                type: 'inside',
                yAxisIndex: [0],
                start: 0,
                end: 100
              }
            ];
          }
          this.chart.setOption(this.chartOption, true);
        }
      },
      drawChart() {
        this.distroyChart();
        this.chart = echarts.init(this.$refs['myChart']);
        this.onChangeDataZoom();
      },
      distroyChart() {
        if (!this.chart) {
          return
        }
        this.chart.dispose();
        this.chart = null;
      },
      getAllData1(doFlush) {
        getAction(this.url.list, {dateType: this.dateType, flushType: doFlush}).then((res) => {
          if (res.success) {
            this.data = [], this.activityData = [], this.countData = [], this.arpuData = [];
            let resultData = res.result;
            let dataFacts = [], dataModel = [];
            let data1 = [], data2 = [], data3 = [];
            let dataSource1 = [], dataSource2 = [], dataSource3 = [];
            Object.keys(resultData).forEach(key => {
              let elData = resultData[key];
              let count = Math.sqrt(elData.count * 25) / 2e2;
              if (count < 1) {
                count = 1;
              }

              if (elData.type === 2) {
                dataFacts.push([
                  //活跃度  ARPU值  群体大小
                  // return Math.sqrt(data[2]) / 5e2;
                  // 竖轴 活跃度
                  // 横轴 价值
                  elData.userValue,
                  elData.userActivity,
                  count,
                  elData.labelName,
                  elData.count,
                  '事实标签用户'
                ])
              } else {
                dataModel.push([
                  //活跃度  ARPU值  群体大小
                  // return Math.sqrt(data[2]) / 5e2;
                  // 竖轴 活跃度
                  // 横轴 价值
                  elData.userValue,
                  elData.userActivity,
                  count,
                  elData.labelName,
                  elData.count,
                  '模型标签用户'
                ])

                data1.push({
                  label: elData.labelName + "(" + elData.count + "人)",
                  value: elData.userActivity
                })
                data2.push({
                  label: elData.labelName + "(" + elData.count + "人) - " + elData.userValue + " 元",
                  value: elData.userValue
                })
                data3.push({
                  label: elData.labelName + "(" + elData.count + "人)",
                  value: Number(Number(Number(elData.count) / Number(this.allUserCount) * 100).toFixed(2))
                })
              }

              if (elData.dataSource === 1) {
                dataSource1.push([
                  //活跃度  ARPU值  群体大小
                  // return Math.sqrt(data[2]) / 5e2;
                  // 竖轴 活跃度
                  // 横轴 价值
                  elData.userValue,
                  elData.userActivity,
                  count,
                  elData.labelName,
                  elData.count,
                  '文件导入'
                ])
              } else if (elData.dataSource === 2 || elData.dataSource === 4 || elData.dataSource === 5) {
                dataSource2.push([
                  //活跃度  ARPU值  群体大小
                  // return Math.sqrt(data[2]) / 5e2;
                  // 竖轴 活跃度
                  // 横轴 价值
                  elData.userValue,
                  elData.userActivity,
                  count,
                  elData.labelName,
                  elData.count,
                  '规则生成'
                ])
              } else if (elData.dataSource === 3) {
                dataSource3.push([
                  //活跃度  ARPU值  群体大小
                  // return Math.sqrt(data[2]) / 5e2;
                  // 竖轴 活跃度
                  // 横轴 价值
                  elData.userValue,
                  elData.userActivity,
                  count,
                  elData.labelName,
                  elData.count,
                  '系统对接'
                ])
              }
            })

            this.data.push(dataFacts, dataModel, dataSource1, dataSource2, dataSource3);

            data1.sort((a, b) => a['value'] < b['value'] ? 1 : -1);
            data2.sort((a, b) => a['value'] < b['value'] ? 1 : -1);
            data3.sort((a, b) => a['value'] < b['value'] ? 1 : -1);
            if (data1.length > 10) {
              this.activityData = data1.slice(0, 10)
            } else {
              this.activityData = data1
            }

            if (data2.length > 10) {
              this.arpuData = data2.slice(0, 10)
            } else {
              this.arpuData = data2
            }

            if (data3.length > 10) {
              this.countData = data3.slice(0, 10)
            } else {
              this.countData = data3
            }

            this.drawChart();
            this.loading = false;
          }
        });
      },
      getAllData(doFlush){
        this.loading = false;
        this.drawChart();
      },
      loadAllUserCount() {
        this.loading = true;
        /*getAction(this.url.loadAllUserCountUrl).then((res) => {
          this.allUserCount = 0
          if (res.success) {
            this.allUserCount = res.result
            this.getAllData();
          } else {*/
            this.allUserCount = 0
          /*}
        })*/
      },
      loadChartAndTable(){
        this.loadPieData();
      },
      viewHistory(row){
        this.drawerLoading = true;
      },
      queryTable(){
        this.loadTableData();
      }
    }
  }
</script>

<style lang="less" scoped>

  .top10-div {
    margin: 14px 0;

    div {
      padding-left: 10px;
    }
  }

  .clear::after {
    content: "";
    display: block;
    clear: both;
  }

  .aa-cs-wrap {
    position: absolute;
    right: 0px;
    top: 1px;
    color: #57a3f3;
    font-weight: normal;
    display: flex;
    align-items: center;
  }

  .mr12 {
    margin-right: 12px;
  }

  .query-condition-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 0 0 200px;
    flex-wrap: nowrap; /* 关键：禁止换行 */
    width: 100%; /* 占满父容器宽度 */
  }

  .query-item {
    margin-right: 12px;
  }

</style>