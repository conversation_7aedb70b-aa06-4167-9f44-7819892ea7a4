<template>
  <div>
    <label-search @searchQuery="initData" ref="labelSearch" style="margin: 0 10px; padding-top: 20px;"
                  @expanded="searchExpanded"/>
    <div style="margin: 0px 10px 10px;">
      <!--<a-radio-group v-model="expandSwitch" button-style="solid">-->
      <!--<a-radio-button value="expand">-->
      <!--展开-->
      <!--</a-radio-button>-->
      <!--<a-radio-button value="merge">-->
      <!--合并-->
      <!--</a-radio-button>-->
      <!--</a-radio-group>-->
      <a-button icon="vertical-align-middle" @click="mergeTree" type="primary" style="margin-left: 12px">合并</a-button>
      <a-button icon="share-alt" @click="showTree" type="primary" style="margin-left: 12px">树图</a-button>
      <a-button icon="deployment-unit" v-if="treeType == '0'" @click="showRelationModel" type="primary"
                style="margin-left: 12px">依赖图谱
      </a-button>
      <template v-if="treeType == '0' || treeType == '2'">
        <a-upload name="uploadFile" :showUploadList="false" :multiple="false" :headers="tokenHeader"
                  :action="importActionUrl" accept=".json" :before-upload="beforeUpload"
                  @change="handleImportLabels">
          <a-button icon="upload" style="margin-left: 12px">模型标签导入</a-button>
        </a-upload>
        <a-button icon="download" :disabled="selectedCodes.length == 0" :loading="iconLoading" style="margin-left: 12px"
                  :title="selectedCodes.length == 0? '请勾选模型标签': ''"
                  @click="handleExportLabels">模型标签规则导出
        </a-button>
        <!--<a-button icon="download" :disabled="selectedCodes.length == 0" :loading="iconLoading" style="margin-left: 12px"-->
        <!--:title="selectedCodes.length == 0? '请勾选模型标签': ''"-->
        <!--@click="handleExportXMindLabels">模型标签XMind导出-->
        <!--</a-button>-->
      </template>
      <a-button icon="download" :disabled="selectedCodes.length == 0" :loading="iconLoading" style="margin-left: 12px"
                :title="selectedCodes.length == 0? '请勾选标签': ''"
                @click="handleExportLabelTrees">标签树导出
      </a-button>
      <a-button icon="download" :disabled="selectedCodes.length == 0" :loading="iconLoading" style="margin-left: 12px"
                :title="selectedCodes.length == 0? '请勾选标签': ''"
                @click="handleExportLabelEvolves">演变数据导出
      </a-button>
      <a-button type="primary" @click="refreshData" icon="reload" style="margin-left: 12px">刷新</a-button>
    </div>
    <div
      class="ant-table ant-table-fixed-header ant-table-layout-fixed ant-table-middle ant-table-scroll-position-left">
      <div class="ant-table-content">
        <div class="ant-table-body">
          <tree-grid
            ref="TreeGrid"
            rowKey="code"
            :height="contentHeight - searchHeight"
            :draggable="true"
            :pageable="pageable"
            :expanded-all="expandSwitch == 'expand'"
            :expanded-row-keys="expandedRowKeys"
            :flag="flag"
            :items='dataSource'
            :columns='columns'
            :loading='loading'
            :expandedAll='true'
            @loading="(loadFlag)=>loading=loadFlag"
            @closeToggle="closeToggle"
            @openToggle="openToggle"
            @updateSort="updateSort">

          <span slot="name" slot-scope="recordMap">
            <span v-if="recordMap.record.bottomNode !== 1">
              <a-badge style="position: absolute;"
                       :numberStyle="{color: '#707070', transform: 'scale(0.7)', 'transform-origin': '-48px -10px', 'background-color': '#8b8b8b29'}"
                       :count="recordMap.record.subCount" :title="'子标签数：' + recordMap.record.subCount"/>
            </span>
            <!-- 使用新添加的 isSpecial 字段 -->
            <a-tag v-if="recordMap.isSpecial" color="blue" style="margin-right: 4px;">上线</a-tag>
            <!--<template v-if="recordMap.record.type === 3 && recordMap.record.bottomNode === 1">-->
            <template v-if="recordMap.record.code.split('.').length > 3">
               <a-checkbox :checked="selectedCodes.includes(recordMap.record.code)" style="margin-right: 4px;"
                           class="lltt-cb-small" @click.stop=""
                           @change="changeCheckedLabel(recordMap.record.code)"/>
            </template>
            <a-icon type="lock" v-if="recordMap.record.isLockDel || recordMap.record.isLockEdit"
                    style="color: #ccc; margin-right: 4px;"
                    :title="(recordMap.record.isLockDel? '删除锁': '') + (recordMap.record.isLockDel && recordMap.record.isLockEdit? '、':'') + (recordMap.record.isLockEdit? '编辑锁':'')"/>
            <a-tooltip placement="topLeft" :title="recordMap.text">
              <a v-if="recordMap.record.bottomNode === 1" @click="openModelRuleInfo(recordMap.record)"
                 class="click-hover-color">{{ recordMap.text }}</a>
              <span v-else>{{ recordMap.text }}</span>
            </a-tooltip>
          </span>
            <span slot="code" slot-scope="recordMap">
            <a-tooltip placement="topLeft" :title="recordMap.text">
                {{ recordMap.text }}
            </a-tooltip>
          </span>
            <span slot="count" slot-scope="recordMap">
            <a v-if="ugsType != 'none'"
               @click="goLabelStatistics(recordMap.record)">{{ initUserCountByLabel(recordMap.text, recordMap.record) }}</a>
            <a-popover placement="bottom" :overlayStyle="{'z-index':1}" v-else>
              <template slot="content">
                <a-button type="primary" size="small" @click="goLabelStatistics(recordMap.record)"
                          style="margin-right: 10px;">用户</a-button>
                <a-button type="primary" size="small" @click="openUserInfo(recordMap.record)">价值</a-button>
              </template>
              <a
                @click="goLabelStatistics(recordMap.record)">{{ initUserCountByLabel(recordMap.text, recordMap.record) }}</a>
            </a-popover>
          </span>
            <span slot="operationDate" slot-scope="recordMap">
            <span type="primary">{{ initCountComplete(recordMap.text, recordMap.record) }}</span>
          </span>
            <span slot="proportion" slot-scope="recordMap">
            <span type="primary">{{ initProportion(recordMap.text, recordMap.record) }}</span>
          </span>
            <span slot="sourceType" slot-scope="recordMap">
          <span v-if="recordMap.text === 'SYSTEM'" type="primary">系统</span>
            <span v-else-if="recordMap.text === 'USER'" type="primary">用户</span>
            <span v-else type="primary">{{ recordMap.text }}</span>
          </span>
            <span slot="dataSource" slot-scope="recordMap">
            <span v-if="recordMap.record.sourceType === '' || recordMap.record.sourceType == null"
                  type="primary"></span>
            <span v-if="recordMap.text === 0" type="primary"></span>
            <span v-else-if="recordMap.text === 1" type="primary">人工导入</span>
            <span v-else-if="recordMap.text === 2" type="primary">规则生成</span>
            <span v-else-if="recordMap.text === 3" type="primary">外部系统</span>
            <span v-else-if="recordMap.text === 4" type="primary">SQL计算</span>
            <span v-else-if="recordMap.text === 5" type="primary">实时标签</span>
            <span v-else-if="recordMap.text === 6" type="primary">预测标签</span>
            <span v-else type="primary">{{ recordMap.text }}</span>
          </span>
            <span slot="entityItemValueType" slot-scope="recordMap">
            <span v-if="recordMap.record.supportDictionary === 1" type="primary">枚举</span>
            <span v-else-if="recordMap.text === 'STRING'" type="primary">字符串</span>
            <span v-else-if="recordMap.text === 'DATE'" type="primary">时间</span>
            <span v-else-if="recordMap.text === 'NUMBER'" type="primary">数字</span>
             <span v-else-if="recordMap.text === 'LIST'" type="primary">列表</span>
            <span v-else type="primary">{{ recordMap.text }}</span>
          </span>
            <span slot="createUser" slot-scope="recordMap">
            <span>{{ recordMap.record.createUserName }}</span>
          </span>
            <span slot="action" slot-scope="recordMap" class="action-btn-split" @click.stop="">
            <a-button v-if="recordMap.record.name === '模型标签' || recordMap.record.name === '预测标签'" type="primary"
                      size="small"
                      @click="handleAddSub(recordMap.record)">添加</a-button>
            <template v-if="recordMap.record.entityItem !== null && recordMap.record.entityItem !== undefined">
              <a-button v-if="recordMap.record.type === 2" type="primary" size="small"
                        @click="statisticsShowByLabelType(recordMap.record)">演变</a-button>
              <a-button v-else-if="recordMap.record.type === 7" type="primary" size="small"
                        @click="statisticsShowByLabelType(recordMap.record)">验证</a-button>
              <a-button v-else type="primary" size="small"
                        @click="statisticsShowByLabelType(recordMap.record)">统计</a-button>
            </template>
              <template v-if="displayEditButton(recordMap.record)">
                <a-popover placement="bottom" :overlayStyle="{'z-index':1}"
                           v-if="recordMap.record.dataSource == 6 || (recordMap.record.dataSource == 2 && recordMap.record.source == 'DRAG')">
                  <template slot="content">
                    <a-button type="primary" size="small" @click="handleEdit(recordMap.record, 'base')"
                              style="margin-right: 10px;">基础编辑</a-button>
                    <a-button type="danger" size="small" @click="handleEdit(recordMap.record)">规则编辑</a-button>
                  </template>
                  <a-button type="primary" size="small" @click="handleEdit(recordMap.record, 'base')">编辑</a-button>
                </a-popover>
                <a-button v-else type="primary" @click="handleEdit(recordMap.record)" size="small">编辑</a-button>
              </template>
            <template v-if="recordMap.record.type === 3 && recordMap.record.dataSource === 1">
            <a-button type="primary" @click="setLabelCode(recordMap.record)"
                      size="small">导入</a-button>
            </template>
            <template v-if="![1, 4, 6].includes(recordMap.record.type) && recordMap.record.bottomNode === 1">
              <a-button type="primary" size="small" @click="onlineLabel(recordMap.record, false)"
                        v-if="recordMap.record.isOnline === 1">下线</a-button>
              <a-button type="primary" size="small" @click="onlineLabel(recordMap.record, true)" v-else>上线</a-button>
            </template>
            <template v-if="recordMap.record.type !== 0 && recordMap.record.type !== 2">
              <a-button
                v-if="[2, 6].includes(recordMap.record.dataSource) && [3, 7].includes(recordMap.record.type) && !recordMap.record.betaType"
                type="primary" :disabled="calcLabels && calcLabels.includes(recordMap.record.code)"
                @click="recalculate(recordMap.record)" size="small">计算</a-button>
            <a-dropdown
              v-if="[0,1,3,4,5,6,7].includes(recordMap.record.type) && !['模型标签', '预测标签'].includes(recordMap.record.name)">
            <a-button type="primary" size="small" class="ant-dropdown-link">更多<a-icon type="down"/>
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item v-if="[1, 0, 4, 6].includes(recordMap.record.type)">
                <a @click="handleAddSub(recordMap.record)">添加</a>
              </a-menu-item>
              <a-menu-item v-if="recordMap.record.betaType !== null && recordMap.record.betaType !== undefined">
                <a @click="handleBeta(recordMap.record)">实验</a>
              </a-menu-item>
              <a-menu-item v-if="[2,3,5,7].includes(recordMap.record.type)">
                <a v-if="recordMap.record.type === 7" @click="statisticsShowByLabelType(recordMap.record)">验证</a>
                <a v-else @click="statisticsShowByLabelType(recordMap.record)">演变</a>
              </a-menu-item>
              <a-menu-item v-if="checkToStatistic(recordMap.record)">
                <a @click="statisticsShowByLabelType(recordMap.record)">统计</a>
              </a-menu-item>
              <a-menu-item v-if="recordMap.record.type === 3 && recordMap.record.dataSource === 1">
                <a @click="updateImportCount(recordMap.record)">刷新</a>
              </a-menu-item>
              <a-menu-item v-if="recordMap.record.type === 3 && recordMap.record.dataSource === 1">
                <a @click="deleteUserLabelRelationByCode(recordMap.record)">清空</a>
              </a-menu-item>
              <a-menu-item v-if="displayDeleteButton(recordMap.record)">
                <a @click="deleteAndChild(recordMap.record)">删除</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
            </template>
          </span>
          </tree-grid>
        </div>
      </div>
    </div>
    <Modal v-model="showExportEvolveConfig" title="演变数据导出">
      <Form ref="exportEvolveConfig" :label-width="120" :model="exportEvolveConfig" :rules="exportEvolveConfigRule">
        <FormItem label="时间范围：" prop="dateRange">
          <DatePicker v-model="exportEvolveConfig.dateRange" type="daterange" placeholder="请选取日期范围"
                      format="yyyy-MM-dd" :editable="false" style="width: 100%"/>
        </FormItem>
      </Form>

      <template #footer>
        <Button type="text" @click="exportEvolveConfigCancel">取消</Button>
        <Button type="primary" @click="exportEvolveConfigOk">确定</Button>
      </template>
    </Modal>
    <label-relation-label-modal ref="labelRelationLabelModal"/>
  </div>
</template>

<script>
  import {Form, FormItem, DatePicker, Modal, Button} from 'view-design';
  import {getAction, postAction} from '@/api/manage'
  import {queryLabelTreeList} from '@/api/api'
  import TreeGrid from "./treeGrid";
  import LabelSearch from "./LabelSearch";
  import LabelRelationLabelModal from "../modules/LabelRelationLabelModal";
  import DateExtendRangePicker from "../../../libs/iview-pro/components/date-extend-picker/date-extend-range-picker";
  import moment from 'moment';

  moment.locale('zh-cn');
  export default {
    name: 'LabelListTableTree',
    components: {
      Form, FormItem, DatePicker, Modal, Button,
      DateExtendRangePicker, LabelRelationLabelModal, LabelSearch, TreeGrid
    },
    props: {
      treeType: {
        type: String,
        default: '0'
      },
      pageable: {
        type: Boolean,
        default: false
      },
      noticeCodeFlag: {
        type: Number,
        default: -1
      },
      tokenHeader: {
        type: Object,
      }
    },
    data() {
      const nowMil = Date.now();
      let dayMil = 3600 * 1000 * 24;
      const daysRangeStart = new Date();
      daysRangeStart.setTime(nowMil - dayMil * 7);
      const daysRangeEnd = new Date(nowMil - dayMil);

      return {
        iconLoading: false,
        selectedCodes: [],
        flag: 0,
        columns: [
          {
            title: '名称',
            key: 'name',
            fixed: 'left',
            width: 330,
            ellipsis: true,
            tree: true,
            scopedSlots: {customRender: 'name'}
          },
          {
            title: '编码',
            align: 'center',
            key: 'code',
            scopedSlots: {customRender: 'code'}
          },
          // {
          //   title: '子标签数',
          //   align: 'center',
          //   key: 'subCount'
          // },
          {
            title: '关联用户数',
            align: 'center',
            key: 'count',
            width: 130,
            scopedSlots: {customRender: 'count'}
          },
          {
            title: '运算完成时间',
            align: 'center',
            key: 'operationDate',
            width: 200,
            scopedSlots: {customRender: 'operationDate'}
          },
          {
            title: '占比',
            align: 'center',
            key: 'proportion',
            scopedSlots: {customRender: 'proportion'},
          },
          // {
          //   title: '类型',
          //   align: 'center',
          //   key: 'sourceType',
          //   scopedSlots: {customRender: 'sourceType'},
          // },
          {
            title: '来源规则',
            align: 'center',
            key: 'dataSource',
            scopedSlots: {customRender: 'dataSource'},
          },
          {
            title: '上下线',
            align: 'center',
            key: 'isOnline',
            customRender: (text, record) => {
              if (record.bottomNode === 1) {
                if ([0, 2].includes(record.type) || ![1, 4, 6].includes(record.type)) {
                  if (text === 1) {
                    return '已上线'
                  } else if (text === 0) {
                    return '已下线'
                  } else {
                    return '待上线'
                  }
                }
              }

              return '';
            }
          },
          {
            title: '创建用户',
            align: 'center',
            key: 'createUser',
            scopedSlots: {customRender: 'createUser'},
          },
          {
            title: '操作',
            align: 'center',
            key: 'action',
            width: 280,
            fixed: 'right',
            scopedSlots: {customRender: 'action'},
          }
        ],
        dataSource: [],
        queryParam: {
          name: '',
          code: '',
          createUser: undefined,
          state: undefined,
          onlineState: undefined
        },
        searchHeight: 76 + 32,
        userList: [],
        // 展开的行，受控属性
        expandSwitch: 'merge',
        shrinkRowKeys: [],
        expandedRowKeys: [],
        allExpandedKeys: [],
        loading: true,

        showExportEvolveConfig: false,
        exportEvolveConfig: {
          dateRange: [daysRangeStart, daysRangeEnd],
          from: 'table', labelCode: '',
        },
        exportEvolveConfigRule: {
          dateRange: [{
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              value = this.exportEvolveConfig.dateRange;
              if (value === null || value === "" || value === undefined || value.length === 0) {
                return callback(new Error('时间范围不能为空'));
              }
              if (value.length === 2 && value[0] && value[1]) {
                return callback();
              }
              return callback(new Error("时间范围不能为空"));
            }
          }],
        },
        url: {
          importExcelUrl: '/label/importLabelRule',
          exportLabelRule: '/label/exportLabelRule',
          exportLabelRuleForXMind: '/label/exportLabelRuleForXMind',
          exportLabelTrees: '/label/exportLabelTrees',
          exportLabelEvolves: '/label/exportLabelEvolves',
          updateLabelSortNum: '/label/updateLabelSortNum',
          getUserList: '/label/getUserList',
          flushLabelCount: '/label/flushLabelCount',
        }
      }
    },
    computed: {
      importActionUrl: vm => window._CONFIG['domianURL'] + vm.url.importExcelUrl,
      calcLabels: vm => Object.keys(vm.$store.state.app.labelProgress),
      contentHeight: vm => vm.$store.state.app.windowHeight - 59,
      userId() {
        return this.$store.getters.userInfo.id;
      },
      username() {
        return this.$store.getters.userInfo.username;
      },
      ugsType: vm => window._CONFIG['ugsType']
    },
    mounted() {
    },
    watch: {
      // expandSwitch(v) {
      //   this.loading = true;
      //   this.$forceUpdate();
      //   this.$nextTick(() => {
      //     if (this.dataTimer) {
      //       clearTimeout(this.dataTimer);
      //     }
      //     this.dataTimer = setTimeout(this.expandTableTree, 3)
      //   });
      // },
      noticeCodeFlag(v) {
        if (v > 0) {
          this.loadData();
        }
      }
    },
    created() {
      if (this.treeType === "2") {
        this.loadData();
      } else {
        this.initData({});
      }
    },
    methods: {
      // 处理树形数据，添加新字段
      processTreeData(treeData) {
        treeData.forEach(item => {
          // 添加新字段
          item.isSpecial = this.checkIfSpecial(item); // 根据业务需求判断是否特殊

          // 如果有子节点，递归处理
          if (item.children && item.children.length > 0) {
            this.processTreeData(item.children);
          }
        });
      },

      // 判断是否是特殊节点
      checkIfSpecial(item) {
        // 根据业务需求定义特殊节点的条件
        // 例如：特定类型或者特定状态的节点
        return item.type === 3 && item.isOnline === 1; // 示例条件，请根据实际需求修改
      },

      searchExpanded(expanded) {
        this.searchHeight = (expanded ? 132 : 76) + 32;
      },
      closeToggle(key) {
        let index = this.expandedRowKeys.indexOf(key);
        if (index > -1) {
          this.expandedRowKeys.splice(index, 1);
        }
      },
      openToggle(key) {
        if (!this.expandedRowKeys.includes(key)) {
          this.expandedRowKeys.push(key);
        }
      },

      displayEditButton(record) {
        if ((record.isLockEdit && (this.username === 'admin' || this.userId === record.createUser)) || !record.isLockEdit) {
          if (!['预测标签', '模型标签', '事实标签', 'UGS'].includes(record.name)) {
            return true;
          }
        }
        return false;
      },
      displayDeleteButton(record) {
        if ((record.isLockDel && (this.username === 'admin' || this.userId === record.createUser)) || !record.isLockDel) {
          if (record.type === 1 || record.type === 3 || record.type === 4 || record.type === 5 || record.type === 6 || record.type === 7) {
            if (record.isOnline !== 1) {
              return true;
            }
          }
        }
        return false;
      },
      checkToStatistic(record) {
        let len = 0;
        if (!!record.children && (len = record.children.length) > 0) {
          let show = true;
          for (let i = 0; i < len; i++) {
            let child = record.children[i];
            let type = child.type;
            if (type !== 2 && type !== 3 && type !== 5 && type !== 7) {
              show = false;
              break;
            }
          }
          return show;
        }
        return false;
      },
      initUserCountByLabel(text, record) {
        if ((record.type === 0 && record.bottomNode !== 1 && record.supportDictionary !== 1) || [1, 4, 6].includes(record.type)) {
          return ''
        }
        let sourceType = record.sourceType
        if (sourceType == null) {
          return ''
        } else {
          if (text == null) {
            return 0
          } else {
            return text
          }
        }
      },
      initCountComplete(text, record) {
        if ((record.type === 0 && record.bottomNode !== 1 && record.supportDictionary !== 1) || record.type === 1 || record.type === -1 || record.type === 4 || record.type === 6) {
          return ''
        } else {
          return text
        }
      },
      initProportion(text, record) {
        if ((record.type === 0 && record.bottomNode !== 1 && record.supportDictionary !== 1) || record.type === 1 || record.type === 4 || record.type === 6) {
          return ''
        }
        let sourceType = record.sourceType
        if (sourceType == null) {
          return ''
        }
        return record.proportionRateStr;
      },
      updateSort(parentkey, childrenKey) {
        this.loading = true
        postAction(this.url.updateLabelSortNum, {
          parentKey: parentkey,
          childrenKey: childrenKey.join(',')
        }).then((res) => {
          this.loadDataBySearch();
          if (!res.success) {
            this.$message.warning("标签顺序后台更新失败，请联系运维人员!")
          }
        })
      },
      getQueryLabelCode() {
        let label = this.$route.query.label;
        if (!label || !label.labelCode || label.labelCode.indexOf('.') == -1) {
          return;
        }
        let code = label.labelCode;
        delete this.$route.query.label;
        return code;
      },
      initData(queryParam, type) {
        this.loading = true;
        queryLabelTreeList({
          name: queryParam.name,
          code: queryParam.code,
          createUser: queryParam.createUser,
          state: queryParam.state,
          isOnline: queryParam.onlineState,
          treeType: this.treeType
        }).then((res) => {
          if (res.success) {
            if (res.result) {
              this.allExpandedKeys = res.result.ids;
              if (type === 'merge') {
                this.mergeTableTree();
              } else if (this.expandedRowKeys.length <= 0) {
                if (this.expandSwitch === 'expand') {
                  let expandedRowKeys = [...this.allExpandedKeys];
                  this.expandedRowKeys = expandedRowKeys;
                } else if (this.expandSwitch === 'merge') {
                  this.mergeTableTree();
                }
              }
              // 处理数据源，添加新字段
              if (res.result.treeList && res.result.treeList.length > 0) {
                this.processTreeData(res.result.treeList);
              }
              this.dataSource = res.result.treeList;
            } else {
              this.$message.warning('没查到对应数据！');
              this.dataSource = [];
              this.expandedRowKeys = [];
            }
          }
        }).finally(() => {
          this.loading = false
        })
      },
      loadDataBySearch(type) {
        this.$refs.labelSearch && this.$refs.labelSearch.searchQuery(type);
      },
      loadData() {
        this.$refs.labelSearch && this.$refs.labelSearch.clearSearch();
        let noticeCode = this.getQueryLabelCode();
        this.$nextTick(() => {
          this.$refs.labelSearch && this.$refs.labelSearch.setSearchCode(noticeCode);
        });

        this.initData({code: noticeCode})
      },
      refreshData() {
        let query = this.$refs.labelSearch && this.$refs.labelSearch.getQuery();
        this.initData(query);
      },
      mergeTree() {
        this.expandSwitch = 'merge';
        this.loading = true;
        this.$forceUpdate();
        this.$nextTick(() => {
          if (this.dataTimer) {
            clearTimeout(this.dataTimer);
          }
          this.dataTimer = setTimeout(this.expandTableTree, 3)
        });
      },
      mergeTableTree() {
        let expandedRowKeys = [];
        for (let i = 0; i < this.allExpandedKeys.length; i++) {
          let key = this.allExpandedKeys[i];
          if (key.split('.').length < 3) {
            expandedRowKeys.push(key);
          }
        }
        this.expandedRowKeys = expandedRowKeys;
      },
      expandTableTree() {
        if (this.expandSwitch === 'expand') {
          let expandedRowKeys = [...this.allExpandedKeys];
          this.expandedRowKeys = expandedRowKeys;
        } else if (this.expandSwitch === 'merge') {
          this.mergeTableTree();
        }
        this.flag++;
        this.$forceUpdate();
        this.$nextTick(() => {
          this.loading = false;
        });
      },
      showTree() {
        this.$router.push({path: '/label/treeView', query: {treeType: this.treeType}});
      },
      showRelationModel() {
        this.$refs.labelRelationLabelModal.open(this.dataSource);
      },
      openModelRuleInfo(record) {
        this.$emit('openModelRuleInfo', record);
      },
      goLabelStatistics(record) {
        this.$emit('goLabelStatistics', record);
      },
      openUserInfo(record) {
        this.$emit('openUserInfo', record);
      },
      handleAddSub(record) {
        this.$emit('handleAddSub', record);
      },
      statisticsShowByLabelType(record) {
        this.$emit('statisticsShowByLabelType', record);
      },
      handleEdit(record, type) {
        this.$emit('handleEdit', record, type);
      },
      recalculate(record) {
        this.$emit('recalculate', record);
      },
      handleBeta(record) {
        this.$emit('handleBeta', record);
      },
      setLabelCode(record) {
        this.$emit('setLabelCode', record);
      },
      updateImportCount(record) {
        getAction(this.url.flushLabelCount, {
          count: record.count || 0,
          code: record.code
        }).then((res) => {
          let msg = record.name + '（' + record.code + '）：' + (res.result.message || res.message);
          if (res.success && res.result.success) {
            if (res.result.message == "更新完成！") {
              this.refreshData();
            }
            this.$message.info(msg)
          } else {
            this.$message.error(msg)
          }
        })
      },
      deleteUserLabelRelationByCode(record) {
        this.$emit('deleteUserLabelRelationByCode', record);
      },
      deleteAndChild(record) {
        this.$emit('deleteAndChild', record);
      },
      onlineLabel(record, isOnline) {
        this.$emit('onlineLabel', record, isOnline);
      },


      /* 导入 */
      handleImportLabels(info) {
        if (info.file.status === 'done') {
          let result = info.file.response;
          if (result.success) {
            this.$message.info('成功导入！');
          } else {
            this.$message.error(`${info.file.name} ${info.file.response.message}.`);
          }
        } else if (info.file.status === 'error') {
          this.$message.error(`文件上传失败: ${info.file.msg} `);
        }
      },
      beforeUpload(file) {
        if (file.type.indexOf('json') < 0) {
          this.$message.error('仅支持JSON文件上传!');
          return false;
        }
        return true;
      },

      handleExportLabels(type) {
        this.iconLoading = true;
        this.$message.info("模型标签正在导出中,请耐心等待.....可以正常使用其他功能", 5);
        let data = new FormData();
        this.selectedCodes.forEach(p => {
          data.append('labelCodes', p);
        });
        postAction(type === 'xmind' ? this.url.exportLabelRuleForXMind : this.url.exportLabelRule, data).then((res) => {
          if (res.success) {
            this.$message.info(res.message + "！可至【输出】-【文件输出】页面查看结果.....");
            this.$router.push({path: '/output/FileInfoList', query: {code: this.code}});
          } else {
            this.$message.error(res.message || "导出失败");
          }
        }).finally(() => {
          this.iconLoading = false;
        });
      },
      handleExportXMindLabels() {
        this.handleExportLabels('xmind');
      },
      handleExportLabelTrees() {
        this.iconLoading = true;
        this.$message.info("模型标签正在导出中,请耐心等待.....可以正常使用其他功能", 5);
        let data = new FormData();
        this.selectedCodes.forEach(p => {
          data.append('labelCodes', p);
        });
        postAction(this.url.exportLabelTrees, data).then((res) => {
          if (res.success) {
            this.$message.info(res.message + "！可至【输出】-【文件输出】页面查看结果.....");
            this.$router.push({path: '/output/FileInfoList', query: {code: this.code}});
          } else {
            this.$message.error(res.message || "导出失败");
          }
        }).finally(() => {
          this.iconLoading = false;
        });
      },
      handleExportLabelEvolves() {
        this.showExportEvolveConfig = true;
        this.exportEvolveConfig.from = 'table';
        this.exportEvolveConfig.labelCode = '';
      },
      oneExportLabelEvolves(labelCode) {
        this.showExportEvolveConfig = true;
        this.exportEvolveConfig.from = 'modal';
        this.exportEvolveConfig.labelCode = labelCode;
      },
      exportEvolveConfigOk() {
        this.$refs.exportEvolveConfig.validate((valid) => {
          if (valid) {
            this.showExportEvolveConfig = false;
            this.iconLoading = true;
            this.$message.info("模型标签正在导出中,请耐心等待.....可以正常使用其他功能", 5);
            let data = new FormData();
            if (this.exportEvolveConfig.from === 'table') {
              this.selectedCodes.forEach(p => {
                data.append('labelCodes', p);
              });
            } else {
              data.append('labelCodes', this.exportEvolveConfig.labelCode);
            }
            data.append('startDate', (this.exportEvolveConfig.dateRange[0] instanceof moment ? this.exportEvolveConfig.dateRange[0] : moment(this.exportEvolveConfig.dateRange[0])).format('YYYY-MM-DD'));
            data.append('endDate', (this.exportEvolveConfig.dateRange[1] instanceof moment ? this.exportEvolveConfig.dateRange[1] : moment(this.exportEvolveConfig.dateRange[1])).format('YYYY-MM-DD'));
            postAction(this.url.exportLabelEvolves, data).then((res) => {
              if (res.success) {
                this.$message.info(res.message + "！可至【输出】-【文件输出】页面查看结果.....");
                if (this.exportEvolveConfig.from === 'table') {
                  this.$router.push({path: '/output/FileInfoList', query: {code: this.code}});
                }
              } else {
                this.$message.error(res.message || "导出失败");
              }
            }).finally(() => {
              this.iconLoading = false;
            });
          }
        })
      },
      exportEvolveConfigCancel() {
        this.showExportEvolveConfig = false;
      },

      changeCheckedLabel(labelCode) {
        let index = this.selectedCodes.indexOf(labelCode);
        if (index > -1) {
          this.selectedCodes.splice(index, 1);
        } else {
          this.selectedCodes.push(labelCode);
        }
      },
    }
  }

</script>
<style lang="less" scoped>

  .click-hover-color {
    cursor: pointer;
  }

  .click-hover-color:hover {
    color: #096dd9;
  }

  .action-btn-split > *:not(:first-of-type) {
    margin-left: 4px;
  }
</style>
<style lang="less">
  .lltt-cb-small .ant-checkbox-inner {
    width: 12px;
    height: 12px;

    &:after {
      top: 45%;
      width: 4px;
      height: 8px;
    }
  }
</style>