<style lang="less" scoped>
.demo-split {
  border: 1px solid #dcdee2;
}

.label-layout {
  /*border: 1px solid #d7dde4;*/
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.label-layout .ivu-icon {
  cursor: pointer;
}

.label-layout .ivu-icon:hover {
  color: var(--fade90);
}

.label-layout .demo-split {

  .right-aa-mb:hover {
    background-color: var(--tint90);
    color: var(--theme);
    border: 1px dashed var(--theme);
    cursor: move;
  }

  .right-select-zone .ivu-tabs-tabpane {
    .label-desc {
      margin: 4px -10px 0;
      padding: 10px;
      box-shadow: inset #0000000f 0px 0px 20px 0px;
      border-top: 1px solid #dcdee2;
      height: 114px;
      color: darkgray;

      h3 {
        margin-bottom: 0.5em;
      }
    }
  }
}

.aa-cs-wrap {
  position: absolute;
  right: 0px;
  top: 1px;
  color: #57a3f3;
  font-weight: normal;
  display: flex;
  align-items: center;
}

.ell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.mt2 {
  margin-top: 2px;
}

.mr12 {
  margin-right: 12px;
}

</style>
<style lang="less">
.label-layout .demo-split {
  .label-selected-title {
    cursor: move;
    display: inline-block;
    padding: 0 4px;
    margin: 0 -4px;
    border: 1px dashed #409eff00;

    &:hover {
      color: var(--theme);
      border-color: var(--theme);
    }
  }

  .disabled {
    cursor: not-allowed;
    color: #c5c8ce;
  }

  .input-font12 {
    font-size: 12px !important;

    input {
      font-size: 12px !important;
    }
  }
}

.label-layout .demo-tree-render.ivu-tree ul {
  font-size: 12px;
}

.demo-tabs-style {
  padding: 16px;
  height: 100%;
}

.demo-tabs-style > .ivu-tabs {
  width: 100%;  /* 确保 Tabs 占满父容器的宽度 */
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar {
  display: flex;
  justify-content: space-between;  /* 使 tab 均匀分布 */
}
.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-nav-container{
  width: 100%;
}
.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-nav-container .ivu-tabs-nav{
  width: 100%;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-tab {
  width: 30%;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  border-color: transparent;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-content {
  margin-top: -16px;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-content > .ivu-tabs-tabpane {
  background: #fff;
  padding: 16px;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-tab-active {
  border-color: #fff;  /* 高亮选中的 tab */
}

/* 外部容器 */
.demo-row-container {
  padding: 20px;
}

/* 每一行 */
.demo-row-container .ivu-row {
  margin-bottom: 16px;  /* 每一行之间增加间距 */
}

/* 每个单元格 */
.demo-row-container .ivu-col {
  text-align: center;  /* 居中对齐 */
  background-color: #fff;  /* 背景色 */
  padding: 16px;  /* 内边距 */
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 美化 <span> 元素 */
.demo-span {
  width: 90px;
  display: inline-block;  /* 让 span 元素变成块级元素，方便设置宽度和高度 */
  background-color: #3498db;  /* 背景色 */
  color: #fff;  /* 文字颜色 */
  padding: 8px 16px;  /* 内边距，增加可点击区域 */
  font-size: 16px;  /* 设置字体大小 */
  font-weight: bold;  /* 加粗字体 */
  border-radius: 10px;  /* 圆角 */
  text-align: center;  /* 居中对齐文字 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);  /* 添加阴影 */
  transition: all 0.3s ease;
}
.demo-span-plain {
  display: inline-block;  /* 让 span 元素变成块级元素 */
  padding: 8px 16px;  /* 保持与第一个 span 元素的内边距一致 */
  font-size: 16px;  /* 字体大小保持一致 */
  font-weight: bold;  /* 加粗字体 */
  color: #333;  /* 设置文字颜色 */
  text-align: center;  /* 居中对齐文字 */
  border-radius: 20px;  /* 圆角 */
  transition: all 0.3s ease;  /* 动画过渡效果 */
}

/* 卡片容器 */
.custom-card-container {
  display: flex;  /* 使用 Flexbox 布局 */
  justify-content: space-between;  /* 卡片之间均匀分布 */
  padding: 20px;  /* 容器内边距 */
  background-color: #fff; /* 背景色设置为白色 */
  gap: 10px;  /* 将卡片之间的间距设置为更小的 10px */
}

/* 卡片样式 */
.custom-card {
  width: 23%;  /* 每个卡片占容器的 22% 宽度，留出间隙 */
  height: 160px;  /* 固定高度 */
  background-color: #fff;  /* 卡片背景色设置为白色 */
  border-radius: 10px;  /* 圆角 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  /* 阴影 */
  display: flex;
  flex-direction: column;  /* 垂直排列 */
  justify-content: center; /* 居中 */
  align-items: center;  /* 居中 */
  transition: all 0.3s ease;  /* 动画过渡效果 */
  padding: 15px 0;  /* 增加上下内边距 */
}

/* 卡片悬停效果 */
.custom-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);  /* 悬停时阴影加重 */
  transform: translateY(-5px);  /* 悬停时轻微上浮 */
}

/* 名称样式 */
.custom-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;  /* 名称与数字之间的间距 */
}

/* 数字样式 */
.custom-card-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

/* 趋势指标样式 */
.custom-card-trend {
  font-size: 12px;
  line-height: 1;
}

/* 柱状图样式 */
.clv-chart {
  cursor: pointer;
}
.clv-chart-container {
  position: relative;
}
.clv-chart-tip {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px 10px;
  border-radius: 4px;
}

/* 无数据容器样式 */
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #fafafa;
  border-radius: 8px;
  margin: 16px;
}

.no-data-content {
  text-align: center;
  padding: 40px 20px;
}


</style>
<template>
  <div width="1200px" class="label-layout ugs-small" style="background-color: #f0f2f5; padding-bottom: 24px;">
    <Row style="padding: 12px 12px 0;">
      <Col :span="24" style="padding-right: 12px;">
        <Card :padding="0" style="border-radius: 14px 0 0; height: 100%;">
          <item-header title-sn="01" title="筛 选"/>
          <UserSearchRuleComp ref="userSearchRuleComp"/>
        </Card>
      </Col>
    </Row>
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
<!--        <ugs-spin fix v-if="metricsLoading"/>-->
        <item-header title-sn="02" title="核 心 指 标">
          <div class="aa-cs-wrap">
            <date-extend-range-picker ref="dateExtendRangePicker" class="mr12" v-model="analysisParams"
                                      :editable="false" type="range" :shortcuts="analysisShortCuts"
                                      :width="290" hide-tab="relative_range"
                                      :disabled-date="analysisDateOptions.disabledDate"
                                      @input="changeAnalysisRangeDate"/>
            <Button class="mr12" icon="md-refresh" type="primary" size="small" @click="loadChartAndTable">分析
            </Button>
          </div>
        </item-header>
        <!-- 核心指标Tab页 -->
          <ugs-spin fix v-if="metricsLoading"  size="large"/>
        <div v-else-if="coreMetricsData.length > 0" style="padding: 16px;">
          <Tabs v-model="activeMetricsTab" type="card" @on-click="handleMetricsTabChange">
            <TabPane v-for="(item, index) in coreMetricsData" :key="index" :label="item.name" :name="item.name">
              <div class="custom-card-container">
                <div class="custom-card">
                  <div class="custom-card-title">用户数</div>
                  <div class="custom-card-number">{{ formatNumber(item.userCount) }}</div>
                  <div class="custom-card-trend">
                    <Icon type="md-people" style="color: #5470c6; margin-right: 4px;" />
                    <span style="color: #5470c6;">人</span>
                  </div>
                </div>
                <div class="custom-card">
                  <div class="custom-card-title">周期价值(元)</div>
                  <div class="custom-card-number">{{ formatCurrency(item.periodValue) }}</div>
                  <div class="custom-card-trend">
                    <Icon type="md-trending-up" style="color: #91cc75; margin-right: 4px;" />
                    <span style="color: #91cc75;">元</span>
                  </div>
                </div>
                <div class="custom-card">
                  <div class="custom-card-title">活跃率</div>
                  <div class="custom-card-number">{{ formatPercentage(item.activeRate) }}</div>
                  <div class="custom-card-trend">
                    <Icon type="md-pulse" style="color: #fac858; margin-right: 4px;" />
                    <span style="color: #fac858;">活跃</span>
                  </div>
                </div>
                <div class="custom-card">
                  <div class="custom-card-title">流失率</div>
                  <div class="custom-card-number">{{ formatPercentage(item.churnRate) }}</div>
                  <div class="custom-card-trend">
                    <Icon type="md-trending-down" style="color: #ee6666; margin-right: 4px;" />
                    <span style="color: #ee6666;">流失</span>
                  </div>
                </div>
              </div>
            </TabPane>
          </Tabs>
        </div>

        <!-- 无数据时的默认展示 -->
        <div v-else class="no-data-container">
          <div class="no-data-content">
            <Icon type="md-analytics" size="48" style="color: #c5c8ce; margin-bottom: 16px;" />
            <p style="color: #999; font-size: 16px; margin-bottom: 8px;">暂无数据</p>
            <p style="color: #c5c8ce; font-size: 14px;">请选择时间区间并点击“分析”按钮获取数据</p>
          </div>
        </div>
        <div style="padding-left: 20px; margin: 10px auto;">
          <span style="display: block; font-size: 14px; color: #666; margin-bottom: 8px;">
              周期价值: 圈选的用户在时间周期内的收入总和
          </span>
          <span style="display: block; font-size: 14px; color: #666;">
              活跃率: 成熟期用户数 ÷ 当前用户总数，反映平台中稳定活跃用户的占比, 流失率: 流失期用户数 ÷ 当前用户总数，衡量当前用户中已进入流失阶段的比例。
          </span>
        </div>
      </Card>
    </Row>
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <ugs-spin fix v-if="chartLoading"/>
        <item-header title-sn="03" title="生 命 周 期"/>
        <div class="clv-chart-container" style="padding: 16px; position: relative;">
          <div ref="clvChart" class="clv-chart" style="width: 100%; height: 400px;" :style="{opacity: chartLoading ? 0.3 : 1}"></div>
        </div>
      </Card>
    </Row>

    <!-- 第四个区域 -->
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <ugs-spin fix v-if="loading"/>
        <item-header title-sn="04" title="详 细 数 据">
          <div style="position: absolute; right: 10px; top: 0;">
            <Button type="primary" size="small" style="margin-right: 10px;" @click="exportUserList">
              <Icon type="md-download" /> 下载
            </Button>
            <Button type="success" size="small" @click="saveAsLabel">
              <Icon type="md-pricetag" /> 保存为标签
            </Button>
          </div>
        </item-header>
        <div style="padding: 16px;">
          <div style="margin-bottom: 16px;">
            <span style="font-size: 16px; font-weight: 500; margin-right: 10px;">当前选择:</span>
            <Tag type="border" color="success" v-if="selectedGroup">人群: {{ selectedGroup }}</Tag>
            <Tag type="border" color="primary" v-if="selectedStage" style="margin-left: 8px;">阶段: {{ selectedStage }}</Tag>
            <Tag v-if="!selectedGroup && !selectedStage">非确定期</Tag>
            <span style="margin-left: 16px;">用户数量: <span style="font-weight: bold;">{{ userCount }}</span></span>
          </div>

          <Table :columns="userColumns" :data="userData" :loading="tableLoading" border style="width: 100%;">
            <template slot-scope="{ row, index }" slot="action">
              <Button type="primary" size="small" style="margin-right: 5px" @click="viewUserDetail(row)">查看</Button>
<!--              <Button type="success" size="small" @click="tagUser(row)">标记</Button>-->
            </template>
          </Table>

          <div style="margin-top: 16px; text-align: right;">
            <Page :total="totalUsers" :current="currentPage" :page-size="pageSize"
                  show-total show-elevator @on-change="changePage" />
          </div>
        </div>
      </Card>
    </Row>

  </div>
</template>
<script>
import {
  Modal,
  ButtonGroup, Button,
  Card,
  Input, Icon,
  Layout,
  Row, Col,
  Sider,
  Split,
  Table,
  Tabs,
  TabPane,
  Page,
  Tag,
  Message,
  Spin,
  RadioGroup, Radio,
  Poptip,Breadcrumb, BreadcrumbItem, Tooltip
} from 'view-design';
import {deleteAction, getAction, httpAction, postAction} from '@/api/manage'

import ItemHeader from "../../main-components/itemCard/ItemHeader";
import moment from 'moment';
import {Empty} from 'ant-design-vue';
import DateExtendRangePicker from "../../../libs/iview-pro/components/date-extend-picker/date-extend-range-picker";

import {formatUsers, formatUsersNoUnit} from "../../main-components/charts/numberFormatter";
import UgsSpin from "../../modules/spin/UgsSpin";
import UserSearchRuleComp from "./comp/UserSearchRuleComp";
import echarts from 'echarts';

moment.locale('zh-cn');
export default {
  name: 'ClvAnalysis',
  components: {
    UserSearchRuleComp,
    UgsSpin,
    DateExtendRangePicker,
    ItemHeader,
    Tooltip,
    Input, Icon,
    ButtonGroup, Button, Modal,
    Table,
    Layout,
    Split,
    Card,
    Sider,
    Tabs, TabPane,
    Row, Col,
    Page,
    Tag,
    Spin,
    RadioGroup, Radio,
    getAction,
    Poptip, Breadcrumb, BreadcrumbItem
  },
  props: {},
  data() {
    const nowMil = Date.now();
    let dayMil = 3600 * 1000 * 24;
    const daysRangeEnd = new Date(nowMil - dayMil);
    const daysRangeStart = new Date();
    daysRangeStart.setTime(nowMil - dayMil * 15);

    const days90 = parseInt(moment(nowMil - dayMil * 91).format('YYYYMMDD'));
    const todayInt = parseInt(moment().format('YYYYMMDD'));

    return {
      analysisParams: {
        type: 'past_15_days', from: 'cut',
      },
      analysisDateParams: [],

      analysisDateOptions: {
        disabledDate(dateInt) {
          return dateInt < days90 || dateInt > todayInt;
        }
      },
      analysisShortCuts: [
        {
          text: '今日',
          code: "past_0_days",
          value() {
            let start = new Date();
            return [start, start];
          }
        },
        {
          text: '昨日',
          code: "past_1_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date(nowMil - dayMil);
            return [start, end];
          }
        },
        {
          text: '本周',
          code: "this_week",
          value() {
            const end = new Date();
            let week = end.getDay();
            const start = new Date();
            if (week === 0) {
              start.setTime(start.getTime() - dayMil * 6);
            } else {
              start.setTime(start.getTime() - dayMil * (week - 1));
            }
            console.log(start, end);
            return [start, end];
          }
        },
        {
          text: '上周',
          code: "last_week",
          value() {
            const end = new Date();
            let week = end.getDay();

            if (week === 0) {
              end.setTime(end.getTime() - dayMil * 7);
            } else {
              end.setTime(end.getTime() - dayMil * week);
            }
            const start = new Date(end.getTime() - dayMil * 6);
            return [start, end];
          }
        },
        {
          text: '本月',
          code: "this_month",
          value() {
            const end = new Date();
            const start = new Date();
            start.setDate(1);
            return [start, end];
          }
        },
        {
          text: '上月',
          code: "last_month",
          value() {
            const end = new Date();
            end.setDate(1);
            end.setTime(end.getTime() - dayMil);
            const start = new Date(end.getTime());
            start.setDate(1);
            return [start, end];
          }
        },
        {
          text: '过去3天',
          code: "past_3_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 3);
            return [start, end];
          }
        },
        {
          text: '过去7天',
          code: "past_7_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 7);
            return [start, end];
          }
        },
        {
          text: '过去15天',
          code: "past_15_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 15);
            return [start, end];
          }
        },
        {
          text: '过去30天',
          code: "past_30_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 30);
            return [start, end];
          }
        },
        {
          text: '过去60天',
          code: "past_60_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 60);
            return [start, end];
          }
        },
        {
          text: '过去90天',
          code: "past_90_days",
          value() {
            const end = new Date(nowMil - dayMil);
            const start = new Date();
            start.setTime(nowMil - dayMil * 90);
            return [start, end];
          }
        },
      ],
      loading: false,
      clvChart: null,
      // 生命周期阶段
      clvStages: ['新客期', '成长期', '成熟期', '衰退期', '流失期'],
      // 生命周期阶段解释
      stageDescriptions: {
        '新客期': '注册时间是最近一个月之内的用户',
        '成长期': '用户月观影时长小于30个小时的用户',
        '成熟期': '用户月观影时长大于30个小时，或者连续多月有消费的用户',
        '衰退期': '之前一个月观影时长大于30小时的用户,当月观影时长下降的用户',
        '流失期': '最近30天无任何观影记录的用户'
      },
      // 多个人群的数据（初始为空，等待API返回）
      clvData: [],
      // 核心指标数据
      coreMetricsData: [],
      // 当前选中的核心指标tab
      activeMetricsTab: '',
      // 核心指标加载状态
      metricsLoading: false,
      // 柱状图加载状态
      chartLoading: false,
      selectedStage: '',
      selectedGroup: '',
      userCount: 0,
      tableLoading: false,
      userData: [],
      userColumns: [
        { title: '用户ID', key: 'gid', minWidth: 100 },
        { title: '注册日期', key: 'registerDate', minWidth: 100 },
        { title: '最近观看时间', key: 'lastWatchTime', minWidth: 150 },
        { title: '当月订单数', key: 'orderCountMonth', minWidth: 100 },
        { title: '当月观看时长', key: 'watchDurationMonth', minWidth: 120,
          render: (h, params) => {
            // 将秒数转换为小时并保留两位小数
            const hours = (params.row.watchDurationMonth / 3600).toFixed(2);
            return h('span', `${hours} 小时`);
          }
        },
        { title: '当月订单金额', key: 'orderAmountMonth', minWidth: 120,
          render: (h, params) => {
            // 将分转换为元并保留两位小数
            const yuan = (params.row.orderAmountMonth / 100).toFixed(2);
            return h('span', `${yuan} 元`);
          }
        },
        { title: '操作', slot: 'action', minWidth: 80, align: 'center' }
      ],
      totalUsers: 0,
      currentPage: 1,
      pageSize: 10
    }
  },
  mounted() {
    // 延迟初始化图表，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initClvChart();
      }, 100);
    });

    // 默认选择过去15天
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 14); // 15天（包括今天）
    this.analysisDateParams = [start, end];

    // 监听窗口大小变化和tab切换
    this.addResizeListener();
  },

  activated() {
    // 当组件被激活时（如tab切换），重新调整图表大小
    this.$nextTick(() => {
      this.resizeChart();
    });
  },

  beforeDestroy() {
    // 清理事件监听器
    this.removeResizeListener();
    if (this.clvChart) {
      this.clvChart.dispose();
    }
  },
  computed: {
  },
  methods: {
    initClvChart() {
      // 检查容器是否存在且可见
      if (!this.$refs.clvChart) {
        console.warn('ECharts容器不存在');
        return;
      }

      // 检查容器宽度
      const containerWidth = this.$refs.clvChart.offsetWidth;
      if (containerWidth <= 100) {
        console.warn('ECharts容器宽度异常:', containerWidth);
        // 延迟初始化
        setTimeout(() => {
          this.initClvChart();
        }, 200);
        return;
      }

      // 初始化图表
      this.clvChart = echarts.init(this.$refs.clvChart);

      // 配置项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            if (!params || params.length === 0) return '';

            const stageName = params[0].name; // 阶段名称
            const stageDesc = this.getStageDescription(stageName);

            let result = `<div style="
              background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
              border: 1px solid #e1e5e9;
              border-radius: 8px;
              padding: 12px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              max-width: 320px;
              min-width: 200px;
              line-height: 1.5;
              word-wrap: break-word;
              word-break: break-all;
              white-space: normal;
              box-sizing: border-box;
            ">`;

            result += `<div style="
              font-weight: 600;
              font-size: 14px;
              color: #1a1a1a;
              margin-bottom: 8px;
              border-bottom: 2px solid #007bff;
              padding-bottom: 4px;
              word-wrap: break-word;
              white-space: normal;
            ">${stageName}</div>`;

            result += `<div style="
              color: #495057;
              font-size: 13px;
              margin-bottom: 10px;
              line-height: 1.6;
              background-color: #f1f3f4;
              padding: 8px;
              border-radius: 4px;
              border-left: 3px solid #007bff;
              word-wrap: break-word;
              word-break: break-all;
              white-space: normal;
              box-sizing: border-box;
            ">${stageDesc}</div>`;

            result += `<div style="border-top: 1px solid #dee2e6; padding-top: 8px;">`;

            params.forEach(param => {
              const color = param.color;
              result += `<div style="
                margin: 6px 0;
                display: flex;
                align-items: center;
                padding: 4px 0;
                flex-wrap: wrap;
                min-height: 20px;
              ">`;
              result += `<span style="
                display: inline-block;
                width: 12px;
                height: 12px;
                background-color: ${color};
                border-radius: 50%;
                margin-right: 10px;
                border: 2px solid #fff;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              "></span>`;
              result += `<span style="
                color: #495057;
                font-size: 13px;
                margin-right: 6px;
                word-wrap: break-word;
                white-space: normal;
              ">${param.seriesName}:</span>`;
              result += `<span style="
                font-weight: 600;
                color: #1a1a1a;
                font-size: 14px;
                white-space: nowrap;
              ">${param.value} 人</span>`;
              result += `</div>`;
            });

            result += `</div></div>`;
            return result;
          }
        },
        legend: {
          data: this.clvData.map(item => item.name),
          top: 10,
          right: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.clvStages,
          axisLabel: {
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        yAxis: {
          type: 'value',
          name: '用户数',
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value} 人'
          }
        },
        series: this.clvData.map(item => ({
          name: item.name,
          type: 'bar',
          barGap: 0,
          data: item.data.map((value, index) => ({
            value,
            itemStyle: {
              color: this.getStageColor(index),
              borderRadius: [5, 5, 0, 0]
            }
          })),
          label: {
            show: true,
            position: 'top',
            formatter: '{c} 人',
            fontSize: 12
          }
        }))
      };

      // 使用配置项设置图表
      this.clvChart.setOption(option);

      // 添加点击事件
      this.clvChart.on('click', (params) => {
        this.handleChartClick(params);
      });

      // 不在这里直接添加resize监听，由addResizeListener统一处理

      // 初始化完成，等待用户点击分析按钮
    },

    // 添加窗口大小变化监听器
    addResizeListener() {
      this.resizeHandler = () => {
        this.resizeChart();
      };
      window.addEventListener('resize', this.resizeHandler);

      // 监听ResizeObserver来检测容器大小变化（如果支持）
      if (this.$refs.clvChart && window.ResizeObserver) {
        try {
          this.resizeObserver = new ResizeObserver(() => {
            this.resizeChart();
          });
          this.resizeObserver.observe(this.$refs.clvChart);
        } catch (error) {
          console.warn('ResizeObserver初始化失败:', error);
        }
      }
    },

    // 移除窗口大小变化监听器
    removeResizeListener() {
      if (this.resizeHandler) {
        window.removeEventListener('resize', this.resizeHandler);
      }
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
      }
    },

    // 调整图表大小
    resizeChart() {
      if (this.clvChart) {
        // 检查容器宽度
        const containerWidth = this.$refs.clvChart ? this.$refs.clvChart.offsetWidth : 0;
        if (containerWidth <= 100) {
          // 容器宽度异常，延迟重试
          setTimeout(() => {
            this.resizeChart();
          }, 100);
          return;
        }

        try {
          this.clvChart.resize();
        } catch (error) {
          console.warn('图表resize失败:', error);
        }
      }
    },
    // 处理图表点击事件
    handleChartClick(params) {
      // params包含了点击的系列名称和类别名称
      const seriesName = params.seriesName; // 人群名称
      const stageName = params.name; // 生命周期阶段

      this.selectedStage = stageName;
      this.selectedGroup = seriesName;
      this.currentPage = 1;

      console.log(`选中了人群 ${seriesName} 的 ${stageName} 阶段`);

      this.loadUserData();
    },
    // 加载用户数据
    loadUserData() {
      this.tableLoading = true;

      // 获取基础参数（参照 /clv/analysis/getResult 接口的参数获取方式）
      if (!this.analysisDateParams || this.analysisDateParams.length !== 2) {
        Message.warning('请选择时间区间');
        this.tableLoading = false;
        return;
      }

      // 获取时间区间
      const startTime = (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD');
      const endTime = (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD');

      // 获取用户筛选规则
      let ruleModel = {};
      try {
        if (this.$refs.userSearchRuleComp) {
          ruleModel = this.$refs.userSearchRuleComp.gainRuleModel() || {};
        }
      } catch (error) {
        console.warn('获取用户筛选条件失败:', error);
      }

      // 构建请求参数
      let params = {
        baseRuleFrontMap: ruleModel,
        startTime: startTime,
        endTime: endTime,
        name: this.selectedStage || '', // 选中的生命周期阶段名称（如“成熟期”、“新客期”等）
        pageNo: this.currentPage,
        pageSize: this.pageSize
      };

      // 如果选中了具体的人群，添加到参数中
      if (this.selectedGroup) {
        params.groupName = this.selectedGroup; // 人群名称作为单独参数
      }

      // 调用详细数据接口
      postAction('/clv/analysis/getDetailData', params).then(res => {

        if (res.success && res.result) {

          // 处理分页数据结构
          if (res.result.records && Array.isArray(res.result.records)) {
            this.userData = res.result.records;
            this.totalUsers = res.result.total || 0;
            this.userCount = res.result.total || 0;

          } else {

            this.userData = [];
            this.totalUsers = 0;
            this.userCount = 0;
          }
        } else {
          Message.error(res.message || '获取详细数据失败');
          this.userData = [];
          this.totalUsers = 0;
          this.userCount = 0;
        }

        this.tableLoading = false;
      }).catch(error => {
        console.error('详细数据接口调用失败:', error);
        Message.error('网络请求失败，请稍后重试');

        this.userData = [];
        this.totalUsers = 0;
        this.userCount = 0;
        this.tableLoading = false;
      });
    },
    // 切换页码
    changePage(page) {
      this.currentPage = page;
      this.loadUserData();
    },
    // 查看用户详情
    viewUserDetail(user) {
      const watchHours = (user.watchDurationMonth / 3600000).toFixed(2);
      const orderAmount = (user.orderAmountMonth / 100).toFixed(2); // 将分转换为元
      Modal.info({
        title: '用户详情',
        content: `
          <p>用户ID: ${user.gid}</p>
          <p>注册日期: ${user.registerDate}</p>
          <p>最近观看时间: ${user.lastWatchTime}</p>
          <p>当月订单数: ${user.orderCountMonth}单</p>
          <p>当月观看时长: ${watchHours}小时</p>
          <p>当月订单金额: ${orderAmount}元</p>
          <p>当前阶段: ${user.currentStage || '未知'}</p>
        `
      });
    },
    // 标记用户
    tagUser(user) {
      Modal.confirm({
        title: '添加标签',
        content: `确定要为用户 ${user.gid} 添加“重点关注”标签吗？`,
        onOk: () => {
          // 这里可以调用标签相关的API
          // postAction('/api/user/addTag', { gid: user.gid, tag: '重点关注' })
          Message.success('标签添加成功');
        }
      });
    },
    // 处理日期范围变化
    changeAnalysisRangeDate(value) {
      this.analysisDateParams = value;
    },

    // 分析按钮点击事件
    doAnalysis() {
      this.loadChartAndTable();
    },
    // 加载图表和表格数据
    loadChartAndTable() {
      if (!this.analysisDateParams || this.analysisDateParams.length !== 2) {
        Message.warning('请选择时间区间');
        return;
      }

      // 检查用户筛选组件是否存在
      if (!this.$refs.userSearchRuleComp) {
        Message.warning('用户筛选组件未初始化');
        return;
      }

      try {
        this.$refs.userSearchRuleComp.changeBaseRuleFrontBo();
      } catch (error) {
        console.warn('用户筛选组件方法调用失败:', error);
      }

      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        // 根据时间区间生成不同的数据
        // 这里可以根据时间区间生成不同的数据
        // 实际项目中应该调用API
        const startTime = (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD');
        const endTime = (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD');
        let ruleModel = {};
        try {
          ruleModel = this.$refs.userSearchRuleComp.gainRuleModel() || {};
        } catch (error) {
          console.warn('获取用户筛选条件失败:', error);
        }

        let params = {
          baseRuleFrontMap: ruleModel,
          startTime: startTime,
          endTime: endTime
        };
        // 分开调用两个API，独立处理渲染
        this.loadChartData(params);
        this.loadMetricsData(params);

        this.loading = false;
      }, 500);
    },

    // 加载柱状图数据
    loadChartData(params) {
      this.chartLoading = true;
      postAction('/clv/analysis/getResult', params).then(res => {
        if (res.success && res.result && res.result.list) {
          this.clvData = res.result.list || [];

          // 更新图表
          if (this.clvChart && this.clvData.length > 0) {
            this.clvChart.setOption({
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                },
                formatter: (params) => {
                  if (!params || params.length === 0) return '';

                  const stageName = params[0].name; // 阶段名称
                  const stageDesc = this.getStageDescription(stageName);

                  let result = `<div style="
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                    border: 1px solid #e1e5e9;
                    border-radius: 8px;
                    padding: 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 320px;
                    min-width: 200px;
                    line-height: 1.5;
                    word-wrap: break-word;
                    word-break: break-all;
                    white-space: normal;
                    box-sizing: border-box;
                  ">`;

                  result += `<div style="
                    font-weight: 600;
                    font-size: 14px;
                    color: #1a1a1a;
                    margin-bottom: 8px;
                    border-bottom: 2px solid #007bff;
                    padding-bottom: 4px;
                    word-wrap: break-word;
                    white-space: normal;
                  ">${stageName}</div>`;

                  result += `<div style="
                    color: #495057;
                    font-size: 13px;
                    margin-bottom: 10px;
                    line-height: 1.6;
                    background-color: #f1f3f4;
                    padding: 8px;
                    border-radius: 4px;
                    border-left: 3px solid #007bff;
                    word-wrap: break-word;
                    word-break: break-all;
                    white-space: normal;
                    box-sizing: border-box;
                  ">${stageDesc}</div>`;

                  result += `<div style="border-top: 1px solid #dee2e6; padding-top: 8px;">`;

                  params.forEach(param => {
                    const color = param.color;
                    result += `<div style="
                      margin: 6px 0;
                      display: flex;
                      align-items: center;
                      padding: 4px 0;
                      flex-wrap: wrap;
                      min-height: 20px;
                    ">`;
                    result += `<span style="
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      background-color: ${color};
                      border-radius: 50%;
                      margin-right: 10px;
                      border: 2px solid #fff;
                      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                    "></span>`;
                    result += `<span style="
                      color: #495057;
                      font-size: 13px;
                      margin-right: 6px;
                      word-wrap: break-word;
                      white-space: normal;
                    ">${param.seriesName}:</span>`;
                    result += `<span style="
                      font-weight: 600;
                      color: #1a1a1a;
                      font-size: 14px;
                      white-space: nowrap;
                    ">${param.value} 人</span>`;
                    result += `</div>`;
                  });

                  result += `</div></div>`;
                  return result;
                }
              },
              legend: {
                data: this.clvData.map(item => item.name)
              },
              series: this.clvData.map(item => ({
                name: item.name,
                type: 'bar',
                barGap: 0,
                data: item.data.map((value, index) => ({
                  value,
                  itemStyle: {
                    color: this.getStageColor(index),
                    borderRadius: [5, 5, 0, 0]
                  }
                })),
                label: {
                  show: true,
                  position: 'top',
                  formatter: '{c} 人',
                  fontSize: 12
                }
              }))
            });

            // 数据更新后调整图表大小
            this.$nextTick(() => {
              this.resizeChart();
            });
          }

          // 加载用户数据
          this.loadUserData();
        } else {
          console.warn('柱状图数据加载失败:', res.message);
          this.clvData = [];
        }
        this.chartLoading = false;
      }).catch(error => {
        console.error('柱状图 API调用失败:', error);
        this.clvData = [];
        this.chartLoading = false;
      });
    },

    // 加载核心指标数据
    loadMetricsData(params) {
      this.metricsLoading = true;
      postAction('/clv/analysis/getAnalysis', params).then(res => {
        if (res.success && res.result && res.result.data) {
          this.coreMetricsData = res.result.data || [];

          // 设置默认选中第一个tab
          if (this.coreMetricsData.length > 0) {
            this.activeMetricsTab = this.coreMetricsData[0].name;
          }
        } else {
          console.warn('核心指标数据加载失败:', res.message);
          this.coreMetricsData = [];
          this.activeMetricsTab = '';
        }
        this.metricsLoading = false;
      }).catch(error => {
        console.error('核心指标 API调用失败:', error);
        this.coreMetricsData = [];
        this.activeMetricsTab = '';
        this.metricsLoading = false;
      });
    },
    // 获取用户群体信息（从核心指标数据中获取）
    getUserGroups() {
      // 优先从核心指标数据中获取
      if (this.coreMetricsData && this.coreMetricsData.length > 0) {
        return this.coreMetricsData.map((group, index) => ({
          name: group.name,
          userNum: group.userCount || 0,
          id: index
        }));
      }

      // 如果核心指标数据不存在，尝试从柱状图数据中获取
      if (this.clvData && this.clvData.length > 0) {
        return this.clvData.map((group, index) => ({
          name: group.name,
          userNum: group.data ? group.data.reduce((sum, value) => sum + value, 0) : 0,
          id: index
        }));
      }

      // 如果都没有数据，返回空数组
      return [];
    },
    // 获取阶段颜色
    getStageColor(index) {
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
      return colors[index % colors.length];
    },

    // 获取阶段解释
    getStageDescription(stageName) {
      return this.stageDescriptions[stageName] || '暂无描述';
    },

    // 数据格式化方法
    formatNumber(value) {
      if (value === null || value === undefined) return '0';
      return value.toLocaleString();
    },

    formatCurrency(value) {
      if (value === null || value === undefined) return '0.00';
      return value.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    formatPercentage(value) {
      if (value === null || value === undefined) return '0.00%';
      return (value * 100).toFixed(2) + '%';
    },

    // 处理核心指标tab切换
    handleMetricsTabChange(name) {
      this.activeMetricsTab = name;
      // 可以在这里添加其他逻辑，比如更新相关数据
    },
    // 导出用户列表
    exportUserList() {
      if (!this.analysisDateParams || this.analysisDateParams.length !== 2) {
        Message.warning('请选择时间区间');
        return;
      }

      const startTime = (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD');
      const endTime = (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD');

      // 实际项目中应该调用API
      // const params = {
      //   startTime: startTime,
      //   endTime: endTime,
      //   stage: this.selectedStage
      // };
      // window.open(`/api/clv/export?startTime=${startTime}&endTime=${endTime}&stage=${this.selectedStage || ''}`);

      Message.success('用户数据导出成功');
    },
    // 保存为标签
    saveAsLabel() {
      Modal.confirm({
        title: '保存为标签',
        content: `确定要将${this.selectedStage || '当前'}用户保存为标签吗？`,
        onOk: () => {
          // 模拟保存标签操作
          setTimeout(() => {
            Message.success('标签保存成功');
          }, 1000);

          // 实际项目中应该调用API
          // const params = {
          //   stage: this.selectedStage,
          //   users: this.userData.map(user => user.userId)
          // };
          // postAction('/api/labels/save', params).then(res => {
          //   if (res.success) {
          //     Message.success('标签保存成功');
          //   } else {
          //     Message.error(res.message || '保存失败');
          //   }
          // }).catch(err => {
          //   Message.error('保存失败');
          // });
        }
      });
    }
  }
}

</script>