<style lang="less" scoped>
.demo-split {
  border: 1px solid #dcdee2;
}

.label-layout {
  /*border: 1px solid #d7dde4;*/
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.label-layout .ivu-icon {
  cursor: pointer;
}

.label-layout .ivu-icon:hover {
  color: var(--fade90);
}

.label-layout .demo-split {

  .right-aa-mb:hover {
    background-color: var(--tint90);
    color: var(--theme);
    border: 1px dashed var(--theme);
    cursor: move;
  }

  .right-select-zone .ivu-tabs-tabpane {
    .label-desc {
      margin: 4px -10px 0;
      padding: 10px;
      box-shadow: inset #0000000f 0px 0px 20px 0px;
      border-top: 1px solid #dcdee2;
      height: 114px;
      color: darkgray;

      h3 {
        margin-bottom: 0.5em;
      }
    }
  }
}

.aa-cs-wrap {
  position: absolute;
  right: 0px;
  top: 1px;
  color: #57a3f3;
  font-weight: normal;
  display: flex;
  align-items: center;
}

.ell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.mt2 {
  margin-top: 2px;
}

.mr12 {
  margin-right: 12px;
}

</style>
<style lang="less">
.label-layout .demo-split {
  .label-selected-title {
    cursor: move;
    display: inline-block;
    padding: 0 4px;
    margin: 0 -4px;
    border: 1px dashed #409eff00;

    &:hover {
      color: var(--theme);
      border-color: var(--theme);
    }
  }

  .disabled {
    cursor: not-allowed;
    color: #c5c8ce;
  }

  .input-font12 {
    font-size: 12px !important;

    input {
      font-size: 12px !important;
    }
  }
}

.label-layout .demo-tree-render.ivu-tree ul {
  font-size: 12px;
}

.demo-tabs-style {
  padding: 16px;
  height: 100%;
}

.demo-tabs-style > .ivu-tabs {
  width: 100%;  /* 确保 Tabs 占满父容器的宽度 */
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar {
  display: flex;
  justify-content: space-between;  /* 使 tab 均匀分布 */
}
.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-nav-container{
  width: 100%;
}
.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-nav-container .ivu-tabs-nav{
  width: 100%;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-tab {
  width: 30%;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  border-color: transparent;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-content {
  margin-top: -16px;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-content > .ivu-tabs-tabpane {
  background: #fff;
  padding: 16px;
}

.demo-tabs-style > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-tab-active {
  border-color: #fff;  /* 高亮选中的 tab */
}

/* 外部容器 */
.demo-row-container {
  padding: 20px;
}

/* 每一行 */
.demo-row-container .ivu-row {
  margin-bottom: 16px;  /* 每一行之间增加间距 */
}

/* 每个单元格 */
.demo-row-container .ivu-col {
  text-align: center;  /* 居中对齐 */
  background-color: #fff;  /* 背景色 */
  padding: 16px;  /* 内边距 */
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 美化 <span> 元素 */
.demo-span {
  width: 90px;
  display: inline-block;  /* 让 span 元素变成块级元素，方便设置宽度和高度 */
  background-color: #3498db;  /* 背景色 */
  color: #fff;  /* 文字颜色 */
  padding: 8px 16px;  /* 内边距，增加可点击区域 */
  font-size: 16px;  /* 设置字体大小 */
  font-weight: bold;  /* 加粗字体 */
  border-radius: 10px;  /* 圆角 */
  text-align: center;  /* 居中对齐文字 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);  /* 添加阴影 */
  transition: all 0.3s ease;
}
.demo-span-plain {
  display: inline-block;  /* 让 span 元素变成块级元素 */
  padding: 8px 16px;  /* 保持与第一个 span 元素的内边距一致 */
  font-size: 16px;  /* 字体大小保持一致 */
  font-weight: bold;  /* 加粗字体 */
  color: #333;  /* 设置文字颜色 */
  text-align: center;  /* 居中对齐文字 */
  border-radius: 20px;  /* 圆角 */
  transition: all 0.3s ease;  /* 动画过渡效果 */
}

/* 卡片容器 */
.custom-card-container {
  display: flex;  /* 使用 Flexbox 布局 */
  justify-content: space-between;  /* 卡片之间均匀分布 */
  padding: 20px;  /* 容器内边距 */
  background-color: #fff; /* 背景色设置为白色 */
  gap: 10px;  /* 将卡片之间的间距设置为更小的 10px */
}

/* 卡片样式 */
.custom-card {
  width: 23%;  /* 每个卡片占容器的 22% 宽度，留出间隙 */
  height: 200px;  /* 固定高度 */
  background-color: #fff;  /* 卡片背景色设置为白色 */
  border-radius: 10px;  /* 圆角 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  /* 阴影 */
  display: flex;
  flex-direction: column;  /* 垂直排列 */
  justify-content: center; /* 居中 */
  align-items: center;  /* 居中 */
  transition: all 0.3s ease;  /* 动画过渡效果 */
}

/* 卡片悬停效果 */
.custom-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);  /* 悬停时阴影加重 */
  transform: translateY(-5px);  /* 悬停时轻微上浮 */
}

/* 名称样式 */
.custom-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;  /* 名称与数字之间的间距 */
}

/* 数字样式 */
.custom-card-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}


</style>
<template>
  <div width="1200px" class="label-layout ugs-small" style="background-color: #f0f2f5; padding-bottom: 24px;">
    <Row style="padding: 12px 12px 0;">
      <Col :span="14" style="padding-right: 12px;">
        <Card :padding="0" style="border-radius: 14px 0 0; height: 100%;">
          <item-header title-sn="01" title="筛 选"/>
          <UserSearchRuleComp ref="userSearchRuleComp"/>
        </Card>
      </Col>
      <Col :span="10">
        <Card :padding="0" style="border-radius: 14px 0 0; height: 100%;">
          <item-header title-sn="02" title="参 数 配 置"/>
          <div class="demo-tabs-style">
            <Tabs>
              <TabPane label="R 消费间隔">
                <div class="demo-row-container">
                  <Row>
                    <Col span="8"><span class="demo-span">1分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{ rfmParams.r2+1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">MAX</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">2分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.r1+1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{ rfmParams.r2 }}</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">3分</span></Col>
                    <Col span="8"><span class="demo-span-plain">0 ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.r1}}</span></Col>
                  </Row>
                </div>
              </TabPane>
              <TabPane label="F 消费频率">
                <div class="demo-row-container">
                  <Row>
                    <Col span="8"><span class="demo-span">1分</span></Col>
                    <Col span="8"><span class="demo-span-plain">0 ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.f1}}</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">2分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.f1+1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{ rfmParams.f2 }}</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">3分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{ rfmParams.f2 + 1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">MAX</span></Col>
                  </Row>
                </div>
              </TabPane>
              <TabPane label="M 消费金额">
                <div class="demo-row-container">
                  <Row>
                    <Col span="8"><span class="demo-span">1分</span></Col>
                    <Col span="8"><span class="demo-span-plain">0 ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{ rfmParams.m1 }}</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">2分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.m1 + 1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.m2}}</span></Col>
                  </Row>
                  <Row>
                    <Col span="8"><span class="demo-span">3分</span></Col>
                    <Col span="8"><span class="demo-span-plain">{{rfmParams.m2+1}} ~ </span></Col>
                    <Col span="8"><span class="demo-span-plain">MAX</span></Col>
                  </Row>
                </div>
              </TabPane>
            </Tabs>
          </div>

        </Card>
      </Col>
    </Row>
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <ugs-spin fix v-if="loading"/>
        <item-header title-sn="03" title="核 心 指 标">
          <div class="aa-cs-wrap">
            <date-extend-range-picker ref="dateExtendRangePicker" class="mr12" v-model="analysisParams"
                                      :editable="false" type="range" :shortcuts="analysisShortCuts"
                                      :width="analysisDateWidth" hide-tab="relative_range"
                                      :disabled-date="analysisDateOptions.disabledDate" vs-clearable
                                      @clickVsBtn="changeAnalysisDateWidth"
                                      @input="changeAnalysisRangeDate"/>
            <Button class="mr12" icon="md-refresh" type="primary" size="small" @click="loadChartAndTable">分析
            </Button>
          </div>
        </item-header>
        <div class="custom-card-container">
          <div class="custom-card">
            <div class="custom-card-title">用户数</div>
            <div class="custom-card-number">{{summaryData.totalNum}}</div>
          </div>
          <div class="custom-card">
            <div class="custom-card-title">交易金额</div>
            <div class="custom-card-number">{{summaryData.monetary}}</div>
          </div>
          <div class="custom-card">
            <div class="custom-card-title">人均交易金额</div>
            <div class="custom-card-number">{{summaryData.avg_m}}</div>
          </div>
          <div class="custom-card">
            <div class="custom-card-title">人均交易频次</div>
            <div class="custom-card-number">{{summaryData.avg_f}} </div>
          </div>
        </div>
        <div style="padding-left: 20px; margin: 10px auto;">
          <span style="display: block; font-size: 16px; font-weight: 500; color: #333; margin-bottom: 12px;">
              对比值: 是交易间隔得分(RS)，交易频次得分(FS)，交易金额得分(MS)分数的平均值(加权平均值)。
          </span>
          <span style="display: block; font-size: 16px; font-weight: 500; color: #333;">
          分别是: RS: {{summaryData.avg_rs}}, FS: {{summaryData.avg_fs}}, MS: {{summaryData.avg_ms}}
          </span>
        </div>
      </Card>
    </Row>
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <ugs-spin fix v-if="loading"/>
        <item-header title-sn="04" title="用 户 构 成">
          <div v-if="chartData.length > 0"  style="position: absolute; right: 0px; top:1px; color: #57a3f3; font-weight: normal; ">
            <Button class="mr12" icon="md-refresh" type="info" size="small" ghost style="width: 70px"
                    @click="loadChart">重置
            </Button>
            <Button class="mr12" icon="md-download" type="info" size="small" ghost style="width: 70px"
                    @click="chartExport">下载
            </Button>
          </div>
        </item-header>
        <a-empty :image="simpleImage" description="暂无数据" v-if="chartData.length <= 0"/>
        <div v-if="chartData.length > 0" style="width: 100%;height: 500px;padding: 20px" ref="rfm_rectangle_tree_1">

        </div>
      </Card>
    </Row>
    <Row style="padding: 12px 12px 0;">
      <Card :padding="0" style="border-radius: 14px 0 0; width: 100%;">
        <ugs-spin fix v-if="tableLoading"/>
        <item-header title-sn="05" title="联 动 明 细">
          <div v-if="chartData.length > 0"  style="position: absolute; right: 0px; top:1px; color: #57a3f3; font-weight: normal; ">
            <Button class="mr12" icon="md-bookmark" type="success" size="small" ghost
                    @click="saveLabel">保存为标签
            </Button>
            <Button class="mr12" icon="md-download" type="info" size="small" ghost style="width: 70px"
                    @click="downloadTable">下载
            </Button>
          </div>
        </item-header>
        <a-empty :image="simpleImage" description="暂无数据" v-if="tableData.length <= 0"/>
        <div v-if="tableData.length > 0" style="padding: 20px 20px 0px;">PS:{{detailGroup}}</div>
        <div v-if="tableData.length > 0" style="width: 100%;padding: 20px;padding-top: 10px">
          <Table border
                 :columns="columns"
                 :data="tableData"
                 :loading="tableLoading"
          ></Table>
<!--          <Row type="flex" justify="end" style="padding-top: 10px">-->
<!--            <Page-->
<!--              :current="paginationConfig.current"-->
<!--              :total="paginationConfig.total"-->
<!--              :page-size="paginationConfig.pageSize"-->
<!--              @on-change="handlePageChange"-->
<!--              @on-page-size-change="handlePageSizeChange"-->
<!--              :page-size-opts="[10, 20, 50, 100]"-->
<!--              size="small"-->
<!--              show-total-->
<!--              show-elevator-->
<!--              show-sizer-->
<!--            ></Page>-->
<!--          </Row>-->
        </div>
      </Card>
    </Row>
    <LabelSaveModal ref="labelSaveModel" @resetFields="resetFields" :base-datas="baseDatas" :final-form="rootForm"
                    :label-info="labelInfo" :label-key-map="labelKeyMap"></LabelSaveModal>

  </div>
</template>
<script>
import {
  Modal,
  ButtonGroup, Button,
  Card,
  Input, Icon,
  Layout,
  Row, Col,
  Sider,
  Split,
  Table,
  Page,
  Tabs,
  TabPane,
  Poptip,Breadcrumb, BreadcrumbItem, Tooltip
} from 'view-design';
import {deleteAction, getAction, httpAction, postAction} from '@/api/manage'

import ItemHeader from "../../main-components/itemCard/ItemHeader";
import moment from 'moment';
import AdvancedAnalysisTargetParent from "./AdvancedAnalysisTargetParent";
import {Empty} from 'ant-design-vue';
import DateExtendRangePicker from "../../../libs/iview-pro/components/date-extend-picker/date-extend-range-picker";

import {formatUsers, formatUsersNoUnit} from "../../main-components/charts/numberFormatter";
import UgsSpin from "../../modules/spin/UgsSpin";
import UserSearchRuleComp from "./comp/UserSearchRuleComp";
import echarts from 'echarts';
import LabelSaveModal from '../../main-components/modal/labelSaveModal.vue'
moment.locale('zh-cn');
export default {
  name: 'AdvancedAnalysis',
  components: {
    LabelSaveModal,
    UserSearchRuleComp,
    UgsSpin,
    DateExtendRangePicker,
    AdvancedAnalysisTargetParent,
    ItemHeader,
    Tooltip,
    Input, Icon,
    ButtonGroup, Button, Modal,
    Table,
    Layout,
    Split,
    Card,
    Sider,
    Tabs,TabPane,
    Row, Col,
    getAction,
    Page,
    Poptip, Breadcrumb, BreadcrumbItem
  },
  props: {},
  data() {
    const nowMil = Date.now();
    let dayMil = 3600 * 1000 * 24;
    const daysRangeEnd = new Date(nowMil - dayMil);
    const daysRangeStart = new Date();
    daysRangeStart.setTime(nowMil - dayMil * 15);
    let dayRanges = this.setDaysRange(daysRangeStart, daysRangeEnd);
    const todayInt = parseInt(moment().format('YYYYMMDD'));

    return {
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      analysisParams: {
        type: 'this_month', from: 'cut',
      },
      analysisDateParams: [daysRangeStart, daysRangeEnd],
      analysisDateWidth: 290,
      analysisShortCuts: [
        {
          text: '本月',
          code: "this_month",
          value() {
            const end = new Date();
            const start = new Date();
            start.setDate(1);
            return [start, end];
          }
        },
        {
          text: '上月',
          code: "last_month",
          value() {
            const end = new Date();
            end.setDate(1);
            end.setTime(end.getTime() - dayMil);
            const start = new Date(end.getTime());
            start.setDate(1);
            return [start, end];
          }
        },
        {
          text: '过去3个月',
          code: "past_3_months",
          value() {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 3);
            return [start, end];
          }
        },
        {
          text: '过去6个月',
          code: "past_6_months",
          value() {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            return [start, end];
          }
        },
        {
          text: '过去1年',
          code: "past_1_year",
          value() {
            const end = new Date();
            const start = new Date();
            start.setFullYear(start.getFullYear() - 1);
            return [start, end];
          }
        },
        {
          text: '本年',
          code: "this_year",
          value() {
            const end = new Date();
            const start = new Date();
            start.setMonth(0);
            start.setDate(1);
            return [start, end];
          }
        },
        {
          text: '去年',
          code: "last_year",
          value() {
            const now = new Date();
            const year = now.getFullYear() - 1;
            const start = new Date(year, 0, 1);
            const end = new Date(year, 11, 31);
            return [start, end];
          }
        }

      ],
      analysisDateOptions: {
        disabledDate(dateInt) {
          return  dateInt > todayInt;
        }
      },
      daysRange: dayRanges,
      vsDaysRange: [],
      rfmParams: {
        r1: 30,
        r2: 60,
        f1: 1,
        f2: 2,
        m1: 10,
        m2:30
      },
      loading: false,
      chartData : [],
      tableLoading: false,
      tableData : [],
      columns: [
        {
          title: '用户id',
          key: 'gid',
          align: 'center'
        },
        {
          title: '消费金额（元）',
          key: 'monetary',
          align: 'center',
          // sortable: true
        },
        {
          title: '消费频次（次）',
          key: 'frequency',
          align: 'center',
          // sortable: true
        },
        {
          title: '最近一次交易间隔（天）',
          key: 'recency',
          align: 'center',
          // sortable: true
        },
      ],
      paginationConfig: {
        current: 1,
        pageSize: 10,
        total: 0,
        pageSizeOptions: [10, 20, 30],
        showTotal: (total) => `总共 ${total} 条数据`
      },
      url: {
        analysis: '/analysis/rfm/getResult',
        userDetail: '/analysis/rfm/getRFMDetail'
      },
      summaryLoading: false,
      summaryData:{
        totalNum : 0,
        monetary: 0,
        avg_m: 0,
        avg_f: 0,
        avg_rs: 0,
        avg_fs: 0,
        avg_ms: 0
      },
      tabs:[],
      detailGroup: '',
      baseDatas: [],
      labelInfo: {
        parentCode: '',
        name: null,
        code: null,
        description: null,
        dataSource: 2,
        betaType: null,
        betaTypeValue: 2,
        isLockDel: 0,
        isLockEdit: 0,
        fixed: 0,
        isOnline: 0,
        incrementUpdateFlag: 'un_update',
        showIncrementUpdateFlag: false,
        cron: 0,
        mutex: 0,
        mutexSort: 1,
        limit:0,
        weight:1,
        // createUser: this.nickname()
      },
      labelInfoTmp: {
        code: null,
      },
      labelKeyMap: {},
      rootForm: {},
      actionMap:{}
    }
  },
  mounted() {
    this.loadChart();
  },
  methods: {
    changeAnalysisDateWidth(name) {
      this.analysisDateWidth = name ? 290 + 270 : 290;
    },
    changeAnalysisRangeDate(v1) {
      if (v1.value && v1.value.length === 2) {
        this.analysisDateParams = v1.value;
      } else if (v1.from === "shortcut") {
        for (let i = 0; i < this.analysisShortCuts.length; i++) {
          let cut = this.analysisShortCuts[i];
          if (cut.code === v1.type) {
            this.analysisDateParams = [...cut.value()];
            break;
          }
        }
      }
    },
    setDaysRange(before, tail) {
      let start = moment(before), end = moment(tail);
      let tmp = [];
      tmp.push(start.format('YYYY-MM-DD'));
      while (start.isBefore(end)) {
        start.add(1, 'day');
        tmp.push(start.format('YYYY-MM-DD'));
      }
      return tmp;
    },
    validatorData() {
      if (!this.$refs.userSearchRuleComp.validatorData()){
        return false;
      }
      return true;
    },

    downloadTable(){

    },
    loadChartAndTable(){
      this.$refs.userSearchRuleComp.changeBaseRuleFrontBo();
      if (!this.validatorData()) {
        return;
      }
      let ruleModel = this.$refs.userSearchRuleComp.gainRuleModel();
      const keys = Object.keys(ruleModel);
      this.tabs = keys
      let params = {
        baseRuleFrontMap: ruleModel,
        rfmParams: this.rfmParams,
        startTime: (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD'),
        endTime: (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD')
      };
      let vsDate = this.$refs.dateExtendRangePicker.getVsData();
      if (vsDate && vsDate.length === 2) {
        params.vsStartTime = vsDate[0].format('YYYY-MM-DD');
        params.vsEndTime = vsDate[1].format('YYYY-MM-DD');
        this.vsDaysRange = this.setDaysRange(vsDate[0], vsDate[1]);
      } else {
        this.vsDaysRange = [];
      }
      this.daysRange = this.setDaysRange(this.analysisDateParams[0], this.analysisDateParams[1]);
      this.loading = true

      postAction(this.url.analysis, params, 600000).then(res => {
        if (res.success) {
            const resultMap = res.result.resultMap
            if(this.tabs.length>0){
              const summaryMap = resultMap[this.tabs[0]]
              this.summaryData.totalNum = summaryMap.total_num;
              this.summaryData.monetary = summaryMap.monetary;
              this.summaryData.avg_m = summaryMap.avg_m;
              this.summaryData.avg_f = summaryMap.avg_f;
              this.summaryData.avg_rs = summaryMap.avg_rs;
              this.summaryData.avg_fs = summaryMap.avg_fs;
              this.summaryData.avg_ms= summaryMap.avg_ms;
              if ( summaryMap.chartData !== undefined && summaryMap.avgChartData !== undefined && summaryMap.groupViewMap !== undefined) {
                this.getChartDataList(
                  summaryMap.chartData,
                  summaryMap.avgChartData,
                  summaryMap.groupViewMap
                );
                this.loadChart()
              }
              this.loading = false
              if(this.chartData.length > 0){
                this.getTableList(this.chartData[0].name,this.chartData[0].viewMap)
              }
            }
        }else{
          this.loading = false
          this.$message.error(res.message)
        }
      })
    },
    getChartDataList(chartData,avgChartData,groupViewMap){
      const chartList = [];
      const keyMapping = {
        high_value_users: '高价值用户',
        key_maintain_users: '重点保持用户',
        key_develop_users: '重点发展用户',
        key_retention_users: '重点挽留用户',
        average_value_users: '一般价值用户',
        average_maintain_users: '一般保持用户',
        average_develop_users: '一般发展用户',
        potential_users: '潜在用户'
      };

      for (const key in keyMapping) {
        if (chartData.hasOwnProperty(key)) {
          chartList.push({
            key: key,
            name: keyMapping[key],
            value: chartData[key],
            avgRecency: avgChartData['avg_recency_'+key],
            avgFrequency: avgChartData['avg_frequency_'+key],
            avgMonetary: avgChartData['avg_monetary_'+key],
            viewMap : groupViewMap[key]
          });
        }
      }
      this.chartData = chartList;
    },
    getTableList(name,viewName){
      this.detailGroup = name
      let params = {
        viewName: viewName,
        startTime: (this.analysisDateParams[0] instanceof moment ? this.analysisDateParams[0] : moment(this.analysisDateParams[0])).format('YYYY-MM-DD'),
        endTime: (this.analysisDateParams[1] instanceof moment ? this.analysisDateParams[1] : moment(this.analysisDateParams[1])).format('YYYY-MM-DD')
      };
      this.tableLoading = true
      postAction(this.url.userDetail, params, 600000).then(res=>{
        if(res.success){
          this.tableData = res.result.map(item => ({
            ...item,
            monetary: (item.monetary / 100).toFixed(2)
          }));
          this.tableLoading = false
        }else {
          this.tableLoading = false
          this.$message.error(res.message)
        }
      });

    },
    loadChart(){
      let color = ['#4B96F3', '#10C6A6', '#F2AF4B', '#FF4D4F', '#AA7AF1'];
      let colorList = [];
      for (let i = 0; i < this.chartData.length; i++) {
        if (i <= 4) {
          colorList[i] = color[i];
        } else {
          colorList[i] = color[i % 5];
        }
      }
      let data = [];
      for (let i = 0; i < this.chartData.length; i++) {
        const item = this.chartData[i];
        data.push({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: colorList[i]
          },
          avgRecency: item.avgRecency,
          avgFrequency: item.avgFrequency,
          avgMonetary: item.avgMonetary,
          viewMap : item.viewMap
        });
      }
      let total = this.summaryData.totalNum
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            // 获取数据
            let name = params.name;
            let value = params.value;
            let percentage = ((value / total ) * 100).toFixed(2);
            let avgRecency = params.data.avgRecency;
            let avgFrequency = params.data.avgFrequency;
            let avgMonetary = params.data.avgMonetary;

            return `${name}: ${value} (${percentage}%)<br/>` +
              `人均消费间隔: ${avgRecency}天<br/>` +
              `人均消费频次: ${avgFrequency}次<br/>` +
              `人均消费金额: ${avgMonetary}元`;
          }
        },
        series: [{
          type: 'treemap',
          width: '100%',
          height: '100%',
          breadcrumb: {
            show: false
          },
          label: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 20,
            },
            formatter: function (params) {
              return `{name|${params.name}}\n{value|${params.value}}`
            },
            rich: {
              name: {
                fontSize: 20,
                fontFamily: "Source Han Sans CN-Regular",
                color: "#fff",
                padding: [6, 0, 0, 6]
              },
              value: {
                fontSize: 20,
                fontFamily: "Source Han Sans CN-Regular",
                color: "#fff",
                padding: [6, 0, 0, 6]
              }
            },
          },
          itemStyle: {
            normal: {
              show: true,
              borderWidth: 1,
              borderColor: '#fff',
            },
            emphasis: {
              label: {
                show: true
              }
            }
          },
          layout: 'horizontal',
          squareRatio: 3,
          roam: false,
          data: data
        }]
      };
      this.$nextTick(()=>{
        const that = this
        const myChart = echarts.init(this.$refs['rfm_rectangle_tree_1']);
        myChart.on('click', function (params) {
          that.getTableList(params.data.name,params.data.viewMap)
        })
        myChart.setOption(option,true);
        window.onresize = myChart.resize
      })

    },
    handlePageChange(page) {
      this.paginationConfig.current = page;
    },
    handlePageSizeChange(newPageSize){
      this.paginationConfig.pageSize = newPageSize;
      this.paginationConfig.current = 1;
    },
    chartExport() {
      const targetChart = echarts.init(this.$refs['rfm_rectangle_tree_1']);
      const chartImgUrl = targetChart.getDataURL({
        pixelRatio: 2,
        backgroundColor: "#fff",
      });
      const fileName = "用户构成";

      this.downloadBase64Image(chartImgUrl, fileName);
    },
    downloadBase64Image(base64, fileName) {
      // 创建一个虚拟的a标签
      let link = document.createElement("a");
      link.href = base64;
      link.download = fileName;

      // 触发点击事件进行下载
      link.click();
      // 下载完成后删除a标签
      setTimeout(function () {
        document.body.removeChild(link);
      }, 100);
    },
    saveLabel() {
      this.baseDatas =  this.$refs.userSearchRuleComp.gainBaseDatas()
      this.rootForm = this.$refs.userSearchRuleComp.gainRootForm()
      this.labelKeyMap = this.$refs.userSearchRuleComp.gainLabelKeyMap()
      this.actionMap = this.$refs.userSearchRuleComp.gainActionMap()
      if (this.checkLabelAnalysis()) {
        this.labelInfo.code = this.labelInfoTmp.code;
        this.labelInfo.showIncrementUpdateFlag = this.showIncrementUpdateFlag(this.baseDatas);
        this.$refs.labelSaveModel.open();
      }
    },
    showIncrementUpdateFlag(datas) {
      for (let i = 0; i < datas.length; i++) {
        let p = datas[i];
        if (p.dateRange === "past_0_days") {
          return true;
        }
        if (p && p.dateRange === 'relative_range' && p.dateValue1 + '' === '0') {
          return true;
        }

        if (p.innerRules && p.innerRules.length > 0) {
          let flag = this.showIncrementUpdateFlag(p.innerRules);
          if (flag) {
            return flag;
          }
        }
      }
      return false;
    },
    // 标签校验
    checkLabelAnalysis() {
      if (this.baseDatas.length <= 0) {
        this.$message.error('未找到可保存的标签信息！');
        return false;
      }
      if (JSON.stringify(this.baseDatas).length > 20000) {
        this.$message.error('规则过多，请减少规则再保存！');
        return false;
      }
      this.loading = true;
      try {
        this.passCheckedLabel = true;
        this.checkLabelData();
        if (!this.passCheckedLabel) {
          this.$message.error('标签有误，请编辑后再保存！');
        }
        return this.passCheckedLabel;
      } finally {
        this.loading = false;
      }
    },
    checkLabelData(element) {
      let bdChildren = element ? element.innerRules : this.baseDatas;
      if (bdChildren && bdChildren.length > 0) {
        for (let i = 0; i < bdChildren.length; i++) {
          this.checkLabelData(bdChildren[i]);
        }
      } else {
        this.checkElement(element);
      }
    },
    //只校验内容（不校验 innerRules 内容）
    checkElement(element) {
      let flag = this.validateElement(element);
      if (flag) {
        element.error = false;
      } else {
        element.error = true;
        this.passCheckedLabel = false;
      }
    },
    validateElement(element) {
      if (element.notEdited && element.judge !== 'true') {
        return false;
      }
      // 行为校验
      if (element.action) {

        let strings = element.action.split("&");
        if (strings.length < 4) {
          return false;
        }

        if (strings[0]) {
          let actionItem = this.actionMap[strings[0]];

          if (actionItem && !this.$refs.userSearchRuleComp.$refs.actionEditModal.checkItemData(element, actionItem.supportType === 'PROPORTE' ? !!element.denominatorBO : false, actionItem.supportType === 'COMPARE' ? !!element.denominatorBO : false)) {
            return false;
          }
        }

        return true;
      }
      return this.$refs.userSearchRuleComp.$refs.ruleEditModal.checkItemData(element);
    },
    resetFields() {
      this.clearFieldsLabelCache();
      this.$message.success('标签保存成功，请前往标签列表查看！');
      this.$forceUpdate();
    },
    clearFieldsLabelCache() {
      this.removeLabelEditFlag();
      this.clearFields();
    },
    removeLabelEditFlag() {
      let code = this.$store.getters.userContentData.labelAnalysis;
      if (code) {
        this.$store.commit('SET_CONTENT_DATA', {labelAnalysis: undefined, closed: false});
        getAction(this.url.removeLabelEditFlag, {labelCode: code}).then((res) => {
        }).finally(() => {
        });
      }
    },
    clearFields(parentCode,mutex) {
      this.baseDatas = [];
      this.rootForm.innerAndOr = '1';
      this.rootForm.negationFlag = 0;
      this.rootForm.userNum = undefined;
      this.rootForm.innerRules = [];
      this.rootForm.initName = '';
      this.passCheckedLabel = true;
      this.labelInfo = {
        parentCode: parentCode || null,
        name: null,
        code: null,
        description: null,
        dataSource: 2,
        betaType: null,
        betaTypeValue: 2,
        // createUser: this.nickname()
        isLockDel: 0,
        isLockEdit: 0,
        fixed: 0,
        isOnline: 0,
        operationSort: 1,
        mutex: mutex || 0,
        mutexSort: 1,
        usedMinSort: undefined,
        incrementUpdateFlag: 'un_update',
        showIncrementUpdateFlag: false,
        cron: 0
      };
      this.labelInfoTmp = {
        parentCode: parentCode || null,
        mutex: mutex || 0,
      };
    },
  }
}

</script>