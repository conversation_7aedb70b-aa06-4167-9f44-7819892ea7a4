<style lang="less" scoped>
  .demo-split {
    border: 1px solid #dcdee2;
  }

  .label-layout {
    /*border: 1px solid #d7dde4;*/
    position: relative;
    border-radius: 4px;
    overflow: hidden;
  }

  .label-layout .ivu-icon {
    cursor: pointer;
  }

  .label-layout .ivu-icon:hover {
    color: var(--fade90);
  }

  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }

  .aa-mb {
    display: inline-block;
    border: 1px solid #ddd;
    /*border-radius: 6px;*/
    height: 28px;
    line-height: 28px;
    width: 120px;
    margin-left: 8px;
    margin-bottom: 16px;
    padding-left: 12px;
    cursor: move;
    border-radius: 6px;
    color: rgb(216, 204, 182);
    box-shadow: #0000002e 0px 1px 4px 0px;
    background-color: rgb(67, 98, 127);

    &.rate-desc {
      position: relative;

      &:after {
        content: '占比';
        position: absolute;
        right: 10px;
        margin: auto;
        font-size: 12px;
        color: gold;
      }
    }

    &.compare-desc {
      position: relative;

      &:after {
        content: '对比';
        position: absolute;
        right: 10px;
        margin: auto;
        font-size: 12px;
        color: gold;
      }
    }
  }

  .aa-mb:hover {
    color: rgb(67, 98, 127);
    box-shadow: #0000002e 0px 1px 4px 0px;
    background-color: rgb(216, 204, 182);

    /*background-color: #eaf4fe;*/
    /*color: #409eff;*/
    border: 1px dashed var(--theme);
  }

  .label-layout .demo-split {

    .right-aa-mb:hover {
      background-color: var(--tint85);
      color: var(--theme);
      border: 1px dashed var(--theme);
      cursor: move;
    }

    .right-select-zone .ivu-tabs-tabpane {
      .label-desc {
        margin: 4px -10px 0;
        padding: 10px;
        box-shadow: inset #0000000f 0px 0px 20px 0px;
        border-top: 1px solid #dcdee2;
        height: 114px;
        color: darkgray;

        h3 {
          margin-bottom: 0.5em;
        }
      }
    }
  }

  .aa-cs-wrap {
    position: absolute;
    right: 0px;
    top: 1px;
    color: #57a3f3;
    font-weight: normal;
    display: flex;
    align-items: center;
  }

  .ell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }

  .mt2 {
    margin-top: 2px;
  }

  .mr12 {
    margin-right: 12px;
  }

</style>
<style lang="less">
  .label-layout .demo-split {
    .label-selected-title {
      cursor: move;
      display: inline-block;
      padding: 0 4px;
      margin: 0 -4px;
      border: 1px dashed #409eff00;

      &:hover {
        color: var(--theme);
        border-color: var(--theme);
      }
    }

    .disabled {
      cursor: not-allowed;
      color: #c5c8ce;
    }

    .input-font12 {
      font-size: 12px !important;

      input {
        font-size: 12px !important;
      }
    }
  }

  .label-layout .demo-tree-render.ivu-tree ul {
    font-size: 12px;
  }

</style>
<template>
  <div style="max-height: 490px; overflow-y: auto; overflow-x: hidden;">
    <div style="margin-top: 2px; font-size: 12px;" v-if="!showDetail">
      <ul>
        <li :draggable="true" :style="{width: 16 * 2 + 40 + 'px'}" :class="{'aa-mb': true}"
            @dblclick.stop="addTagTree('标签', {type: 'label'})"
            @dragstart.stop="drag('label', '标签', {type: 'label'})">
          <a-icon type="tag" style="margin-right: 8px;"/>
          <span style="letter-spacing: 4px">人群</span>
        </li>
        <li :draggable="true"
            :style="{width: 16 * actionItem.entityName.length + 40 + (!['PROPORTE', 'COMPARE'].includes(actionItem.supportType)? 0: 36) + 'px'}"
            :class="{'aa-mb': true, 'rate-desc': actionItem.supportType === 'PROPORTE', 'compare-desc': actionItem.supportType === 'COMPARE'}"
            v-for="(actionItem, actionIndex) in actionList" :key="actionIndex"
            @dblclick.stop="addTagTree(actionItem.entityName, {entityId: actionItem.entityId})"
            @dragstart.stop="drag('action', actionItem.entityName, {key:actionItem.entityId})">
          <a-icon :type="getActionIconType(actionItem)" style="margin-right: 8px;"/>
          <span style="letter-spacing: 4px">{{actionItem.entityName}}</span>
        </li>
      </ul>
    </div>
    <div :style="contentParentStyle">
      <Row :style="{background: '#fff'}">
        <Card style="width: 100%" :padding="0">
          <Tabs v-model="baseRuleFrontActiveKey" type="card" style="width: 100%; " v-if="showTab"
                :closable="baseRuleFrontBONames.length > 1"
                @on-tab-remove="removeBaseRuleFrontBO"
                @on-click="activeBaseRuleFrontBO">
            <TabPane :key="baseRuleFrontBONames[pageIndex]" :name="baseRuleFrontBONames[pageIndex]"
                     :label="h=>getBaseRuleFrontLabelName(h, pageIndex)" :index="pageIndex + 1"
                     v-if="pageIndex < baseRuleFrontBOMax"
                     v-for="(page, pageIndex) in baseRuleFrontBOList"></TabPane>
            <TabPane v-if="baseRuleFrontBONames.length < baseRuleFrontBOMax" key="add" name="aa_add_btn_"
                     :closable="false"
                     icon="md-add"
                     :index="10" :label="getBaseRuleFrontAddButton"/>
          </Tabs>
          <div
            style="width: calc(100% - 4px); position: relative; min-height: 200px; max-height: 348px;overflow: auto; "
            @drop="dropBtn($event, -1, 'label', '')"
            @dragover="allowDrop($event)">
            <Row v-for="(baseData, index) in baseDatas" :key="index">
              <DragBoxItemGroup
                :style="index === 0? 'margin-top: -32px;':''"
                :is-last-one="index === baseDatas.length - 1"
                :draggable="!showDetail"
                :moveItem="!showDetail && dragData.type==='base' && dragData.indexArr[0] === index"
                :cover="!showDetail && getCover(index)"
                :readonly="showDetail"
                :groupData="baseData"
                :level="0"
                :totalLevel="totalLevels[index]" :height-diff="-20"
                :indexArr="[index]"
                :dragData="dragData"
                :userNumLoading="userNumLoading || loading"
                @changeInnerAndOr="changeInnerAndOr"
                @changeNegationFlag="changeNegationFlag"
                @copy="copy"
                @edit="edit"
                @remove="remove"
                @refresh="refresh"
                @clearCalculationCache="clearCalculationCache"
                @move="move"
                @dragOver="dragover"
                @overReplace="overReplace"
                @overSort="overSort"
                @overAdd="overAdd"
                @overCombine="overCombine"
                @resultClick="resultClick"
              ></DragBoxItemGroup>
            </Row>
          </div>
        </Card>
        <div class="ivu-modal-footer"
             style="width: 100%;position: relative;background-color: white;padding: 8px;">
          <div style="text-align: center;float: left;width:100%;"
               v-if="baseDatas && baseDatas.length > 1">
            <div>
              <a @click="resultClickFinal()" style="text-decoration: underline;">
                <Numeral :value="rootForm.userNum" format="0,0"/>
              </a>
              <Icon :type="!rootForm.userNumLoading? 'md-refresh':''" title="刷新"
                    @click="userNumLoading? emptyFunc():refreshFinal()"
                    :style="userNumLoading? 'color: #e6e6e6; cursor: not-allowed; margin-left: 10px;' : 'margin-left: 10px;'"/>
              <span class="dotting" v-if="rootForm.userNumLoading"></span>
            </div>
            <div style="margin-top: -20px;text-align: left;">
              <Button shape="circle" size="small" style="margin-right: 20px; margin-bottom: 4px;"
                      :type="rootForm.innerAndOr === '1'? 'success':'primary'" ghost
                      @click="changeInnerAndOr()">{{rootForm.innerAndOr === '1'?'且':'或'}}
              </Button>
            </div>
          </div>
        </div>
      </Row>
    </div>

    <UserCountModal ref="userCountModal" @changeIsOk="changeIsOk" :clickIsOpen="isOpen"
                    :sendData="sendData"></UserCountModal>
    <RuleEditModal ref="ruleEditModal" :item-map="itemMap" :label-tables="treeData" show-label-tables
                   @changeRule="changeActionRule" @changeLabelSearch="changeLabelSearch"/>
    <ActionEditModal ref="actionEditModal" :item-map="actionItemMap" :action-map="actionMap"
                     :action-options="actionOptions" :action-item-options="actionItemOptions"
                     :statistics-options="statisticsOptions" :done-or-not-options="doneOrNotOptions"
                     :done-options="doneOptions"
                     :action-data-layout="actionDataLayout"
                     @changeRule="changeActionRule"></ActionEditModal>
    <advanced-analysis-tab-name-modal ref="advancedAnalysisTabNameModal" @ok="changeTabName"/>
  </div>
</template>
<script>
  import {h} from 'vue';
  import DragBoxItemGroup from "../../../main-components/dragBoxCompontent/dragBoxItemGroup.vue";
  import {
    Modal,
    ButtonGroup, Button,
    Card,
    Collapse,
    Input, Icon,
    Layout,
    Panel,
    Row, Col,
    Sider,
    Split,
    Table,
    Tree,
    Poptip,
    Tabs,
    TabPane, Breadcrumb, BreadcrumbItem, Tooltip, Form, FormItem
  } from 'view-design';
  import '../../../main-components/dragBoxCompontent/dragBox.css';
  import {queryLabelTreeList} from '@/api/api'
  import {randomUUID} from '@/utils/util.js'
  import RuleEditModal from "../../../main-components/modal/ruleEditModal";
  import ActionEditModal from "../../../main-components/modal/actionEditModal";
  import {deleteAction, getAction, httpAction, postAction} from '@/api/manage'
  import {mapGetters} from 'vuex'
  import Numeral from "../../../../libs/iview-pro/components/numeral/numeral";

  import {matchElementBaseData, setElementWidth, getLevel} from '../../labelCalculation.js';
  import VerticalTab from "../../../main-components/tab/VerticalTab";
  import UserCountModal from "../../user/modules/UserCountModal";
  import ItemHeader from "../../../main-components/itemCard/ItemHeader";
  import GroupSelect from "../../../../libs/iview-pro/components/group-select/group-select";
  import DateExtendRangePicker from "../../../../libs/iview-pro/components/date-extend-picker/date-extend-range-picker";
  import AdvancedAnalysisTabNameModal from "../AdvancedAnalysisTabNameModal";

  import UgsSpin from "../../../modules/spin/UgsSpin";

  export default {
    name: 'UserSearchRuleComp',
    components: {
      UgsSpin,
      AdvancedAnalysisTabNameModal,
      DateExtendRangePicker,
      GroupSelect,
      ItemHeader,
      Tooltip, Form, FormItem,
      UserCountModal,
      VerticalTab,
      Numeral,
      RuleEditModal,
      ActionEditModal,
      DragBoxItemGroup,
      Input, Icon,
      ButtonGroup, Button, Modal,
      Table,
      Layout,
      Split,
      Card,
      Tree,
      Sider,
      Row, Col,
      getAction,
      Collapse, Panel, Poptip, Tabs, TabPane, Breadcrumb, BreadcrumbItem
    },
    props: {
      showDetail: {
        type: Boolean,
        default: false
      },
      baseRuleFrontBOMax: {
        type: Number,
        default: 5
      },
      showTab: {
        type: Boolean,
        default: true
      },
      messageTitle: {
        type: String,
        default: ''
      }
    },
    data() {
      let baseDatas = [];

      return {
        baseRuleForm: {
          parentId: undefined,
          parentNames: [],
          leaf: 1,
          name: '',
          id: undefined,
          nameError: '',
          nameOld: ''
        },
        submitBtnLoading: false,
        passCheckedLabel: true,
        userNumLoading: false,
        loading: false,
        statisticsOptions: [],
        statisticsOptionsMap: {},
        actionItemOptions: {type: 'list', data: []},
        actionItemOptionsMap: {},
        actionDataLayout: {id: '', showDateRange: true},
        doneOrNotOptions: [],
        doneOptions: [],
        baseRuleFrontBOList: [{innerAndOr: '1', userNum: 0, innerRules: baseDatas}],
        baseRuleFrontBONames: ['人群 1'],
        baseRuleFrontBONameIndex: 1,
        baseRuleFrontActiveKey: '人群 1',
        baseRuleFrontBONameForm: {index: -1, name: ''},
        baseDatas: baseDatas,
        rootForm: {
          innerAndOr: '1',
          userNum: 0,
          innerRules: [],
          boIndex: 0
        },
        innerConditionTypes: ['action'], // 用来区分处理标签(label),观影(ub),交易(vas)
        dragData: {
          title: '',
          key: '',
          parentTitle: '',
          type: '',
          indexArr: [-1, -1, -1],
          coverIndexArr: [-1, -1, -1],
        },
        totalLevels: [],
        initTreeData: [],
        treeData: [],
        labelKeyMap: {},
        sendData: {},
        isOpen: false,
        itemMap: {},
        actionItemMap: {},
        actionOptions: [],
        actionOptionsMap: {},
        actionList: [],
        actionMap: {},
        url: {
          getCodeAndLabelMap: '/label/getCodeAndLabelMap',
          getDragModelLabelUserNum: '/label/getDragModelLabelUserNum',
          clearCalculationCache: '/label/clearCalculationCache',
          getActionEntityItemInfo: '/userInsight/getActionEntityItemInfo',
          getActionStatisticsOptions: '/userInsight/getActionStatisticsOptions',
          getActionConditionEntityItemInfo: '/ruleBehaviorAction/getRuleBehaviorActionItems',
          getLabelByLabelCode: '/label/getLabelByLabelCode',

          actionList: '/ruleBehaviorAction/actionList',

          add: 'labelBasicRule/saveOrUpdate',

        },
      };
    },
    created() {
      this.loadTreeData();
      this.initCodeAndLabelMap();
      this.initPrepareData();
      this.initData();

    },
    methods: {
      ...mapGetters(["nickname"]),
      emptyFunc() {
      },
      initDataByLabelInfoRuleStr(ruleJson, userNum){
        if (ruleJson !== null && ruleJson !== undefined && ruleJson !== '') {
          let data = JSON.parse(ruleJson);
          let tmpData = [];
          if (data.innerRules && data.innerRules.length > 0) {
            // this.rootForm.userNum = data.userNum;
            this.rootForm.innerAndOr = data.innerAndOr;
            this.rootForm.negationFlag = data.negationFlag || 0;
            tmpData = data.innerRules;
          } else {
            data.userNum = this.rootForm.userNum;
            tmpData.push(data);
          }
          let len = tmpData.length;
          for (let i = 0; i < len; i++) {
            matchElementBaseData(tmpData[i]);
          }
          this.baseRuleFrontBOList[0] = {innerAndOr: this.rootForm.innerAndOr, userNum: userNum, innerRules: tmpData};
          this.baseDatas = tmpData;
          for (let i = 0; i < len; i++) {
            this.setBaseDataWidth(i)
            this.updateTotalLevel(i)
          }
        }
      },
      addBaseRuleFrontBO() {
        if (this.baseRuleFrontBOList.length >= this.baseRuleFrontBOMax) {
          return;
        }
        this.baseRuleFrontBONameIndex++;
        let name = '人群 ' + this.baseRuleFrontBONameIndex;
        this.baseRuleFrontBONames.push(name);
        this.baseRuleFrontBOList.push({
          innerAndOr: '1',
          userNum: 0,
          innerRules: []
        });
        this.baseRuleFrontActiveKey = name;
        this.clearRootBaseDatas(this.baseRuleFrontBOList[this.baseRuleFrontBOList.length - 1], this.baseRuleFrontBONames.length - 1);
      },
      removeBaseRuleFrontBO(name) {
        let len = this.baseRuleFrontBOList.length;

        let removeIndex = this.baseRuleFrontBONames.indexOf(name);
        let activeIndex = this.rootForm.boIndex;

        if (removeIndex === activeIndex) {
          if (removeIndex + 1 === len) {
            this.baseRuleFrontActiveKey = this.baseRuleFrontBONames[activeIndex - 1];
            this.clearRootBaseDatas(this.baseRuleFrontBOList[activeIndex - 1], activeIndex - 1);
          } else {
            this.baseRuleFrontBOList.splice(removeIndex, 1);
            this.baseRuleFrontBONames.splice(removeIndex, 1);
            this.clearRootBaseDatas(this.baseRuleFrontBOList[activeIndex], activeIndex);
            return;
          }
        } else if (activeIndex > removeIndex) {
          this.rootForm.boIndex--;
        }
        this.baseRuleFrontBOList.splice(removeIndex, 1);
        this.baseRuleFrontBONames.splice(removeIndex, 1);
      },
      changeBaseRuleFrontBo() {
        this.baseRuleFrontBOList[this.rootForm.boIndex] = {
          userNum: this.rootForm.userNum,
          innerRules: JSON.parse(JSON.stringify(this.baseDatas)),
          innerAndOr: this.rootForm.innerAndOr,
          initName: this.rootForm.initName,
          historyInitName: this.rootForm.historyInitName,
        };
      },
      activeBaseRuleFrontBO(name) {
        this.changeBaseRuleFrontBo();
        if (name === 'aa_add_btn_') {
          this.addBaseRuleFrontBO();
          this.totalLevels = [];
          return;
        }
        let index = this.baseRuleFrontBONames.indexOf(name);
        if (index === this.rootForm.boIndex) {
          return;
        }
        this.clearRootBaseDatas(this.baseRuleFrontBOList[index], index);
      },
      getBaseRuleFrontLabelName(h, pageIndex) {
        let name = this.baseRuleFrontBONames[pageIndex];
        return h('span', null, [h('span', {style: {fontSize: '12px'}}, name), h('a-button', {
          style: {
            fontSize: '12px', border: 'none'
          },
          attrs: {
            icon: 'edit', shape: "circle", size: "small", type: 'text'
          },
          on: {
            click: () => {
              this.baseRuleFrontBONameForm = {index: pageIndex, name: name};
              this.$refs.advancedAnalysisTabNameModal.open(name, this.checkTabNameFunc);
            },
          }
        })]);
      },
      checkTabNameFunc(name) {
        for (let i = 0; i < this.baseRuleFrontBONames.length; i++) {
          if (i !== this.baseRuleFrontBONameForm.index) {
            if (this.baseRuleFrontBONames[i] === name) {
              this.$Message.error('重名！');
              return false;
            }
          }
        }
        return true;
      },
      changeTabName(name) {
        this.baseRuleFrontBONames[this.baseRuleFrontBONameForm.index] = name;
        if (this.baseRuleFrontBONameForm.name === this.baseRuleFrontActiveKey) {
          this.baseRuleFrontActiveKey = name;
        }

        this.$forceUpdate();
      },
      getBaseRuleFrontAddButton(h) {
        return h('div', {
          attrs: {title: '最多只能添加5组对照人群组'},
          style: {
            position: 'absolute',
            top: 0, left: 0,
            width: '100%', height: '100%',
          },
        })
      },
      initPrepareData() {
        // 初始化行为标签
        this.initActionEntityItemMap()
      },
      initCodeAndLabelMap() {
        getAction(this.url.getCodeAndLabelMap, {}).then((res) => {
          if (res.success
          ) {
            this.itemMap = res.result;
          } else {
            console.log(res.message)
          }
        })
      },
      loadTreeData() {
        this.treeData = [];
        queryLabelTreeList({treeType: '3'}).then((res) => {
          if (res.success) {
            let treeData = res.result.treeList || [];
            treeData.forEach(d => {
              this.setLabelTable(d);
              d.expand = true;
              d.disabled = true;
            });

            this.initTreeData = treeData;
            this.initLabelKeyMapData();
            this.changeLabelSearch();
          }
        })
      },

      // 数据处理
      initLabelKeyMapData() {
        this.labelKeyMap = {};
        if (this.initTreeData.length > 0) {
          this.setLabelKeyMapData(this.initTreeData)
        }
      },
      setLabelKeyMapData(items, parentKey, parentKeyArr) {
        for (let index = 0; index < items.length; index++) {
          let item = items[index];
          let key = item.code, title = item.title;
          let tmpParentKeyArr = parentKeyArr ? [...parentKeyArr] : undefined;
          if (item.bottomNode === 1 && !Number.isInteger(item.operationSort)) {
            let codeType = item.key;
            item.operationSort = codeType.endsWith('_3') ? 1 : 0;
          }
          this.labelKeyMap[key] = {
            key: key, title: title, parent: parentKey, operationSort: item.operationSort,
            parentArr: tmpParentKeyArr,
            hasChildren: item.bottomNode !== 1
          };
          if (item.children) {
            if (!tmpParentKeyArr) {
              tmpParentKeyArr = [];
            }
            tmpParentKeyArr.push(key);
            this.setLabelKeyMapData(item.children, key, tmpParentKeyArr);
          }
        }
      },

      changeLabelSearch(searchValue) {
        if (this.initTreeData.length <= 0) {
          this.treeData = [];
          return;
        }

        if (!searchValue) {
          this.treeData = JSON.parse(JSON.stringify(this.initTreeData));
        } else {
          let codeKeys = [];
          Object.keys(this.labelKeyMap).forEach(item => {
            let value = this.labelKeyMap[item];
            if (!value.hasChildren) {
              if (value.title.includes(searchValue)) {
                if (!codeKeys.includes(value.key)) {
                  codeKeys.push(value.key);
                }
                if (value.parent) {
                  value.parentArr.forEach(code => {
                    if (!codeKeys.includes(code)) {
                      codeKeys.push(code);
                    }
                  })
                }
              }
            }
          });

          let tmp = [];
          if (codeKeys.length > 0) {
            console.log(searchValue, codeKeys)
            this.loadTreeDataByCodes(this.initTreeData, tmp, codeKeys, true);
          }
          this.treeData = tmp;
        }
      },

      setLabelTable(item, parentName) {
        if (item.children && item.children.length > 0) {
          item.disabled = true;
          for (let index = 0; index < item.children.length; index++) {
            this.setLabelTable(item.children[index], parentName ? [...parentName, item.title] : [item.title]);
          }
        } else {
          if (!item.key.endsWith('_1')) {
            item.label = [...parentName, item.title].join('·');
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        }
      },

      loadTreeDataByCodes(items, targetItems, codeKeys, expand) {
        for (let index = 0; index < items.length; index++) {
          let item = items[index];
          let key = item.code;
          if (codeKeys.includes(key)) {
            let tmpObj = {
              code: key,
              count: item.count,
              key: item.key,
              title: item.title,
              label: item.label,
              value: item.value,
              parentCode: item.parentCode,
              usedMinSort: item.usedMinSort,
              operationSort: item.operationSort,
              expand: expand,
              disabled: item.disabled
            };
            if (item.children) {
              tmpObj.children = [];
            }
            targetItems.push(tmpObj);
            if (item.children) {
              this.loadTreeDataByCodes(item.children, tmpObj.children, codeKeys, true);
            }
          }
        }
      },


      // 树：拼装父节点名称至根节点
      getName(root, nodeKey) {
        // let parentNode = root[nodeKey];
        // let strings = parentNode.node.key.split("_");
        // let type = strings[1];
        // if (type === '2') {
        //   return root[parentNode.parent].node.title + '·' + parentNode.node.title;
        // }else if(type === '3'|| type === '5'){
        return this.getParentName(root, nodeKey);
        // }
        // return parentNode.node.title;
      },
      // 树：拼装父节点名称至根节点
      getParentName(root, nodeKey) {
        let parentNode = root[nodeKey];
        if (parentNode.parent >= 0) {
          return this.getParentName(root, parentNode.parent) + '·' + parentNode.node.title;
        }
        return parentNode.node.title;
      },

      // 数据层级：最大值
      updateTotalLevel(index) {
        if (index >= 0) {
          if (this.baseDatas[index]) {
            this.totalLevels[index] = getLevel(this.baseDatas[index]);
          }
        } else {
          if (this.baseDatas.length > 0) {
            for (let i = 0; i < this.baseDatas.length; i++) {
              this.totalLevels[i] = getLevel(this.baseDatas[i]);
            }
          } else {
            this.totalLevels = [];
          }
        }
      },
      // 编辑
      edit(data, indexArr) {
        if (this.showDetail){
          return;
        }
        console.log(data, data.ruleKey);
        let isAction = false;
        if (data.action) {
          isAction = true;
          let strings = data.action.split('&');
          let entityId = strings[0];
          this.actionDataLayout.showDateRange = this.actionMap[entityId].showTimeInterval === 1;
          if (entityId === '17') {
            this.doneOrNotOptions = [{"key": "开始拥有", "value": "1"}];
          } else if (entityId === '5') {
            this.actionDataLayout.showDateRange = true;
            this.doneOrNotOptions = [
              {"key": "做过", "value": "1"}, {"key": "未做过", "value": "0"},
              {"key": "首次做过", "value": "2"}, {"key": "末次做过", "value": "3"}
            ];
            this.doneOptions = [
              {"key": "做过", "value": "1"},
              {"key": "首次做过", "value": "2"}, {"key": "末次做过", "value": "3"}
            ];
          } else {
            this.doneOrNotOptions = [{"key": "做过", "value": "1"}, {"key": "未做过", "value": "0"}];
            this.doneOptions = [{"key": "做过", "value": "1"}];
          }
          this.actionOptions = this.actionOptionsMap[entityId];
          this.statisticsOptions = this.statisticsOptionsMap[entityId];
          this.actionItemOptions = this.actionItemOptionsMap[entityId];
        }
        if (isAction) {
          this.$refs.actionEditModal.open(data, indexArr);
        } else {
          this.$refs.ruleEditModal.open(data, indexArr);
        }
      },
      clearCalculationCache(data, indexArr) {
        if (this.userNumLoading) {
          this.$message.warning('数据计算中，请稍后再试！')
          return;
        }
        if (data.initName) {
          getAction(this.url.clearCalculationCache, {initName: data.initName}).then((res) => {
            this.$message.success(res.message);
          })
        } else {
          this.$message.warning('无需清除缓存');
        }
      },
      changeActionRule(itemData, indexArr) {
        let item = this.getDataIndexArr(indexArr);
        if (JSON.stringify(item) === JSON.stringify(itemData)) {
          return;
        }
        let oldItemInitName = item.initName;
        if (itemData.initName) {
          delete itemData.initName;
          item = this.getDataIndexArr(indexArr, true, true);
          delete item.initName;
        }
        let keys = Object.keys(item);
        keys.forEach(e => {
          if (e != 'userNumLoading') {
            item[e] = itemData[e];
          }
          delete itemData[e];
        });

        Object.keys(itemData).forEach(e => {
          item[e] = itemData[e];
        });
        item.userNum = 0;
        if (oldItemInitName) {
          item.historyInitName = oldItemInitName;
        }
      },
      // 加载各业务下对应的行为选项
      initActionEntityItemMap() {
        this.actionOptionsMap = {};
        getAction(this.url.getActionEntityItemInfo).then((res) => {
          if (res.success) {
            let datas = res.result;
            if (datas) {
              datas.forEach(e => {
                let el = this.actionOptionsMap[e.entityId];
                if (!el) {
                  el = [];
                  this.actionOptionsMap[e.entityId] = el;
                }
                el.push(e);
              });
              this.initLoadActionList();
            }
            console.log(this.actionOptions);
          } else {
            console.error(res.message);
            this.$Message.error('行为标签初始化失败,请刷新重试!');
          }
        })
      },

      // 加载初始化行为列表
      initLoadActionList() {
        this.actionList = [];
        this.actionMap = {};
        getAction(this.url.actionList).then((res) => {
          if (res.success) {
            this.actionList = res.result || [];
            this.actionList.forEach(p => {
              p.itemName = p.entityName;
              p.children = this.actionOptionsMap[p.entityId];
              this.actionMap[p.entityId] = p;
            });
            this.loadStatisticsOptions();
            this.loadActionRuleEntityItemMap();
          } else {
            console.error(res.message);
            this.$Message.error('行为标签列表初始化失败,请刷新重试!');
          }
        })
      },
      loadStatisticsOptions() {
        this.actionList.forEach(e => {
          this.statisticsOptionsMap[e.entityId] = [];
          getAction(this.url.getActionStatisticsOptions, {"entityId": e.entityId}).then((res) => {
            if (res.success) {
              this.statisticsOptionsMap[e.entityId] = res.result;
              console.log(this.statisticsOptions);
            } else {
              console.error(res.message);
              this.$Message.error('行为标签(二级条件)初始化失败,请刷新重试!');
            }
          })
        });
      },
      loadActionRuleEntityItemMap() {
        this.actionList.forEach(e => {
          this.actionItemOptionsMap[e.entityId] = {type: 'list', data: []};
        });
        getAction(this.url.getActionConditionEntityItemInfo).then((res) => {
          if (res.success) {
            let dataArray = Object.values(res.result || {});
            if (dataArray && dataArray.length > 0) {
              dataArray.forEach(data => {
                let entityId = undefined;
                let tmpActionItemData = {base: [], group: {}}, tmpItemIdentify = [];
                for (let i = 0; i < data.length; i++) {
                  let groupElData = data[i];
                  if (groupElData && groupElData.items && groupElData.items.length > 0 && groupElData.entityId) {
                    if (entityId === undefined) {
                      entityId = groupElData.entityId;
                    }

                    for (let j = 0; j < groupElData.items.length; j++) {
                      let elData = groupElData.items[j];
                      elData.itemIdentify = (elData.selectEntityId || entityId) + '&' + elData.itemIdentify
                        + '&' + elData.indexName
                        + '&' + (elData.searchEntityId === undefined || elData.searchEntityId === null ? '' : elData.searchEntityId)
                        + '&' + (elData.searchConditionField === undefined || elData.searchConditionField === null ? '' : elData.searchConditionField)
                        + '&' + (elData.searchOutputField === undefined || elData.searchOutputField === null ? '' : elData.searchOutputField)
                        + '&' + (elData.useBit === undefined || elData.useBit === null ? '' : elData.useBit);

                      if (tmpItemIdentify.includes(elData.itemIdentify)) {
                        continue;
                      }
                      tmpItemIdentify.push(elData.itemIdentify);
                      elData.itemName = elData.indexName;
                      elData.itemValueType = elData.selectType;
                      elData.entityItemValueType = elData.selectType;

                      let tmpChild = elData.groupName ? tmpActionItemData.group[elData.groupName] : tmpActionItemData.base;
                      if (!tmpChild) {
                        tmpChild = [];
                        tmpActionItemData.group[elData.groupName] = tmpChild;
                      }
                      tmpChild.push(elData);
                      this.actionItemMap[elData.itemIdentify] = elData;
                    }
                  }
                }

                if (Object.keys(tmpActionItemData.group).length > 0) {
                  let data = [{itemName: '默认分组', children: tmpActionItemData.base}];
                  Object.keys(tmpActionItemData.group).forEach(p => {
                    data.push({itemName: p, children: tmpActionItemData.group[p]});
                  });
                  this.actionItemOptionsMap[entityId] = {type: 'group', data: data};
                } else {
                  this.actionItemOptionsMap[entityId] = {type: 'list', data: tmpActionItemData.base};
                }
              });

              console.log(this.actionItemOptionsMap);
            } else {
              console.error(res.result);
              this.$Message.error('行为标签(子条件)初始化：无数据配置,请刷新重试!');
            }
          } else {
            console.error(res.message);
            this.$Message.error('行为标签(子条件)初始化失败,请刷新重试!');
          }
        })
      },
      // 移除
      remove(indexArr) {
        this.removeDataIndexArr(indexArr);
        this.updateTotalLevel(indexArr[0]);
        this.$forceUpdate();
        this.initUserNumLoading();
      },
      // 刷新
      refreshFinal() {
        if (this.userNumLoading) {
          this.$message.warning('数据计算中，请稍后再试！')
          return;
        }
        let ruleModel;
        if (this.baseDatas.length === 0) {
          return;
        }
        if (this.baseDatas.length === 1) {
          ruleModel = this.baseDatas[0];
        } else {
          this.rootForm.innerRules = this.baseDatas;
          ruleModel = this.rootForm;
        }
        this.passCheckedLabel = true;
        this.checkLabelData(ruleModel);
        if (this.passCheckedLabel) {
          this.userNumLoading = true;
          ruleModel.userNumLoading = true;
          let uuid = randomUUID();
          ruleModel.userNumId = uuid;
          if (!ruleModel.initName) {
            ruleModel.initName = 'label' + this.userId + Date.now();
          }
          postAction(this.url.getDragModelLabelUserNum, ruleModel, 600000).then((res) => {
            delete ruleModel.historyInitName;
            this.updateUserNumToTabs(uuid, res.success ? res.result : 0);
            this.initUserNumLoading();
          }).catch(error => {
            this.$message.warning('数据计算异常，请核对配置后再试！')
            this.updateUserNum(undefined, uuid, 0);
            this.initUserNumLoading();
          })
        } else {
          this.$message.warning('请编辑规则完整条件后，再进行计算！')
        }
      },
      refresh(data, indexArr) {
        if (this.userNumLoading) {
          this.$message.warning('数据计算中，请稍后再试！')
          return;
        }
        this.passCheckedLabel = true;
        this.checkLabelData(data);
        if (this.passCheckedLabel) {
          this.userNumLoading = true;
          data.userNumLoading = true;
          let uuid = randomUUID();
          data.userNumId = uuid;
          if (!data.initName) {
            data.initName = 'label' + this.userId + Date.now();
          }
          postAction(this.url.getDragModelLabelUserNum, data, 600000).then((res) => {
            console.log("用户数量：" + res.result, uuid)
            delete data.historyInitName;
            this.updateUserNumToTabs(uuid, res.success ? res.result : 0);
            this.initUserNumLoading();
          }).catch(error => {
            this.$message.warning('数据计算异常，请核对配置后再试！')
            this.updateUserNum(undefined, uuid, 0);
            this.initUserNumLoading();
          })
        } else {
          this.$message.warning('请编辑规则完整条件后，再进行计算！')
        }
      },
      updateUserNumToTabs(uuid, userNum) {
        let flag = this.updateUserNum(undefined, uuid, userNum);
        if (!flag) {
          for (let i = 0; i < this.baseRuleFrontBOList.length; i++) {
            flag = this.updateUserNum(this.baseRuleFrontBOList[i], uuid, userNum);
            if (flag) {
              return;
            }
          }
        }
      },
      updateUserNum(element, uuid, userNum) {
        if (!element && this.rootForm.userNumId == uuid) {
          this.rootForm.userNumId = '';
          this.rootForm.userNumLoading = false;
          this.rootForm.userNum = userNum;
          return true;
        }
        let bdChildren = element ? element.innerRules : this.baseDatas;
        if (bdChildren && bdChildren.length > 0) {
          for (let i = 0; i < bdChildren.length; i++) {
            if (bdChildren[i].userNumId == uuid) {
              bdChildren[i].userNumLoading = false;
              bdChildren[i].userNumId = '';
              bdChildren[i].userNum = userNum;
              return true;
            }
            if (this.updateUserNum(bdChildren[i], uuid, userNum)) {
              return true;
            }
          }
        } else if (element && element.userNumId == uuid) {
          element.userNumId = '';
          element.userNumLoading = false;
          element.userNum = userNum;
          return true;
        }
        return false;
      },
      // 计算是否存在刷新
      initUserNumLoading(element) {
        if (!this.setUserNumLoading()) {
          this.userNumLoading = false;
        }
      },
      setUserNumLoading(element) {
        if (!element && this.rootForm.userNumLoading && this.baseDatas.length > 1) {
          this.userNumLoading = true;
          return true;
        }
        let bdChildren = element ? element.innerRules : this.baseDatas;
        if (bdChildren && bdChildren.length > 0) {
          for (let i = 0; i < bdChildren.length; i++) {
            if (bdChildren[i].userNumLoading) {
              this.userNumLoading = true;
              return true;
            }
            if (this.setUserNumLoading(bdChildren[i])) {
              return true;
            }
          }
        } else if (element && element.userNumLoading) {
          this.userNumLoading = true;
          return true;
        }
        return false;
      },
      // 复制
      copy(data, arr) {
        let objString = JSON.stringify(this.getDataIndexArr(arr, true, true));
        let obj2 = JSON.parse(objString);
        let target = this.addChildren(obj2);
        let tmpArr = [];
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] !== undefined && arr[i] !== -1) {
            tmpArr.push(arr[i]);
          }
        }
        if (tmpArr.length > 1) {
          tmpArr.pop();
        }
        this.combineDataIndexArr(tmpArr, target);
        this.updateTotalLevel(arr[0]);
      },
      // 切换：且、或
      changeInnerAndOr(indexArr) {
        if (this.rootForm.initName) {
          this.rootForm.historyInitName = this.rootForm.initName;
          delete this.rootForm.initName;
        }
        this.rootForm.userNum = 0;
        if (indexArr === undefined) {
          this.rootForm.innerAndOr = this.rootForm.innerAndOr === '1' ? '0' : '1';
          return;
        }
        let d = this.baseDatas;
        for (let i = 0; i < indexArr.length; i++) {
          if (indexArr[i] !== undefined && indexArr[i] !== -1) {
            d = i === 0 ? d[indexArr[i]] : d.innerRules[indexArr[i]];
            if (d.initName) {
              d.historyInitName = d.initName;
              delete d.initName;
            }
            d.userNum = 0;
          }
        }
        d.innerAndOr = d.innerAndOr === '1' ? '0' : '1';
        this.$forceUpdate()
      },
      // 切换：是、非
      changeNegationFlag(indexArr) {
        if (this.showDetail){
          return;
        }
        if (this.rootForm.initName) {
          this.rootForm.historyInitName = this.rootForm.initName;
          delete this.rootForm.initName;
        }
        this.rootForm.userNum = 0;
        if (indexArr === undefined) {
          this.rootForm.negationFlag = this.rootForm.negationFlag === 1 ? 0 : 1;
          return;
        }
        let d = this.baseDatas;
        for (let i = 0; i < indexArr.length; i++) {
          if (indexArr[i] !== undefined && indexArr[i] !== -1) {
            d = i === 0 ? d[indexArr[i]] : d.innerRules[indexArr[i]];
            if (d.initName) {
              d.historyInitName = d.initName;
              delete d.initName;
            }
            d.userNum = 0;
          }
        }
        d.negationFlag = d.negationFlag === 1 ? 0 : 1;
        this.$forceUpdate();
      },
      // 拖拽元素
      move(indexArr) {
        this.dragData.type = 'base';
        this.initIndexDataArr(this.dragData.indexArr);
        this.initIndexDataArr(this.dragData.coverIndexArr);
        for (let i in indexArr) {
          let index = indexArr[i];
          if (typeof index === "number") {
            index = index === undefined ? -1 : index;
            this.dragData.indexArr[i] = index;
          }
        }
      },
      // 初始化临时数组变量
      initIndexDataArr(indexDataArr) {
        for (let i in indexDataArr) {
          if (typeof indexDataArr[i] === "number") {
            indexDataArr[i] = -1;
          }
        }
      },
      // 覆盖元素
      dragover(data, indexArr) {
        for (let i = 0; i < indexArr.length; i++) {
          indexArr[i] = indexArr[i] === undefined ? -1 : indexArr[i];
        }
        this.dragData.coverIndexArr = indexArr;
      },
      getCover(index) {
        if (this.dragData.coverIndexArr[0] === index) {
          if (this.dragData.indexArr.length > 0 && this.dragData.indexArr[0] === index) {
            for (let j = 1; j < this.dragData.indexArr.length; j++) {
              return this.dragData.indexArr[j] !== undefined && this.dragData.indexArr[j] !== -1;
            }
          }
          return true;
        }
        return false;
      },
      // 左侧标签树拖拽节点记录
      drag(type, parentName, item) {
        this.dragData.type = type;
        if (type === 'action') {
          this.dragData.key = this.getFirstActionByEntityId(item.key);
        } else if (type === 'label' && item.type === 'label') {
          this.dragData.key = null;
        } else {
          this.dragData.key = item.key;
        }
        this.dragData.indexArr = [-1, -1, -1];
        this.dragData.coverIndexArr = [-1, -1, -1];
        this.dom = parentName;
      },
      // 覆盖右侧可放区域：根元素
      allowDrop(ev) {
        ev.preventDefault();
        this.dragData.coverIndexArr = [-1, -1, -1];
      },
      // 点击结果值显示趋势图
      resultClickFinal() {
        let ruleModel;
        if (this.baseDatas.length === 0) {
          return;
        }
        if (this.baseDatas.length === 1) {
          ruleModel = this.baseDatas[0];
        } else {
          this.rootForm.innerRules = this.baseDatas;
          ruleModel = this.rootForm;
        }

        this.resultClick(ruleModel);
      },
      resultClick(data) {
        if (data.userNum === 0) {
          this.$message.warning('暂无人群可分析，请点【刷新】按钮或重新编辑规则刷新人数！');
          return;
        }
        this.sendData = data;
        this.isOpen = true;
        this.$refs.userCountModal.loadData(data);
      },
      changeIsOk(val) {
        this.isOpen = val;
      },
      // 拖拽放置元素--覆盖元素进行合并组合
      overCombine(data, indexArr) {
        this.dropEndIndexArr('combine', indexArr);
      },
      // 拖拽放置元素--覆盖元素进行追加拖拽元素
      overAdd(data, indexArr) {
        this.dropEndIndexArr('add', indexArr);
      },
      // 拖拽放置元素--覆盖元素进行替换拖拽元素
      overReplace(data, indexArr) {
        this.dropEndIndexArr('replace', indexArr);
      },
      // 拖拽放置元素--覆盖元素与拖拽元素进行排序处理
      overSort(data, indexArr) {
        this.dropEndIndexArr('sort', indexArr);
      },
      // 拖拽放置元素--右侧可放区域：根元素
      dropBtn(ev, index, type, action) {
        ev.stopPropagation();
        this.dropEndIndexArr(action, [index]);
      },
      checkCombineArray(arr, arr2) {
        let flag = true, i = 0, len = arr2.length;
        arr.forEach((el, index) => {
          i = index;
          if (el != -1) {
            if (arr2[index] == -1) {
              return;
            }
            if (el != arr2[index]) {
              flag = false;
              return flag;
            }
          }
        });

        if (flag) {
          if (len >= i + 1 + 2) {
            if (arr2[i + 1] != -1 && arr2[i + 2] != -1) {
              console.log('eqArray  flag', flag)
              return false;
            }
          }
        }
        console.log('eqArray  flag', flag)
        return flag;
      },
      // 拖拽元素 与 覆盖元素 最终处理逻辑
      dropEndIndexArr(action, indexArr) {
        let moveType = this.dragData.type, moveIndexArr = this.dragData.indexArr;
        this.dragData.type = '';
        this.dragData.indexArr = [-1, -1, -1];
        this.dragData.coverIndexArr = [-1, -1, -1];
        console.log(indexArr, moveIndexArr, moveType)
        if (moveType === 'base' && action == 'combine') {
          if (this.checkCombineArray(indexArr, moveIndexArr)) {
            return;
          }
        }
        if (!this.checkRuleLimit(indexArr)) {
          return;
        }
        if (indexArr[0] === -1) {
          this.addFirstData(moveType, moveIndexArr);
          return;
        }
        if (moveType === 'base') {
          let target;
          switch (action) {
            case 'replace':
            case 'add':
              break;
            case 'sort':
              target = this.removeDataIndexArr(moveIndexArr);
              this.addDataIndexArr(indexArr, target);
              this.updateTotalLevel();
              return;
            case 'combine':
              if (this.isMoveAfterCover(moveIndexArr, indexArr)) {
                target = this.removeDataIndexArr(moveIndexArr);
                target = this.addChildren(target);
                this.combineDataIndexArr(indexArr, target);
              } else {
                target = this.getDataIndexArr(moveIndexArr);
                target = this.addChildren(target);
                this.combineDataIndexArr(indexArr, target);
                target = this.removeDataIndexArr(moveIndexArr);
              }
              break;
            default:
          }
        } else {
          switch (action) {
            case 'add':
              this.addDataIndexArr(indexArr, this.getEmptyData(this.dom, this.dragData.key, moveType));
              break;
            case 'replace':
              this.replaceData(indexArr, this.getEmptyData(this.dom, this.dragData.key, moveType));
              break;
            case 'sort':
              break;
            case 'combine':
              let target = this.getEmptyData(this.dom, this.dragData.key, moveType);
              target = this.addChildren(target);
              this.combineDataIndexArr(indexArr, target);
              break;
            default:
              this.addTagTree(this.dom, this.dragData.key);
              return;
          }
        }
        if (indexArr[0] !== undefined && indexArr[0] !== -1) {
          this.updateTotalLevel(indexArr[0]);
        }
      },
      replaceData(indexArr, replaceData) {
        let data = this.getDataIndexArr(indexArr, true);
        if (data.initName) {
          data.historyInitName = data.initName;
          delete data.initName;
        }
        this.replaceDataElement(data, replaceData);
      },
      // 拖拽元素 在 覆盖元素|放置元素 之后，返回true；否则 false
      isMoveAfterCover(moveIndexArr, coverIndexArr) {
        if (moveIndexArr[0] === coverIndexArr[0]) {
          if (moveIndexArr[1] === coverIndexArr[1]) {
            return moveIndexArr[2] > coverIndexArr[2];
          }
          return moveIndexArr[1] > coverIndexArr[1];
        }
        return moveIndexArr[0] > coverIndexArr[0];
      },
      // 添加子元素
      addChildren(data) {
        if (!data) {
          return data;
        }
        delete data.historyInitName;
        delete data.initName;
        let innerRules = data.innerRules || [];
        if (innerRules.length === 0) {
          let objString = JSON.stringify(data);
          let obj2 = JSON.parse(objString);
          innerRules.push(obj2);
        }
        data.innerRules = innerRules;
        data.userNum = 0;
        data.innerAndOr = '1';
        data.negationFlag = 0;

        // 置空作为逻辑框的该层data中的标签规则信息
        data.title = null;
        data.index = null;
        data.notEdited = null;
        data.width = null;
        data.ruleKey = null;
        data.ruleValue1 = null;
        data.ruleValue2 = null;
        data.judge = null;
        data.actionInnerRules = null;
        data.dateJudge = null;
        data.pastOrFuture = null;
        data.relativeRange = null;
        data.dataType = null;
        data.interval = null;
        data.doneOrNot = null;
        data.action = null;
        data.dateValue1 = null;
        data.dateValue2 = null;
        data.dateRange = null;
        data.ruleDesc = null;
        data.actionInnerRulesDesc = null;
        data.userNumId = null;
        data.userNumLoading = null;
        data.dateTimeRange = null;
        data.dateTimeValue1 = null;
        data.dateTimeValue2 = null;
        return data;
      },
      // 根据索引号获取元素
      getDataIndexArr(indexArr, removeInitName, userNumZore) {
        if (removeInitName && this.rootForm.initName) {
          this.rootForm.historyInitName = this.rootForm.initName;
          delete this.rootForm.initName;
        }
        if (userNumZore && this.rootForm.userNum > 0) {
          this.rootForm.userNum = 0;
        }
        let d = this.baseDatas;
        if (removeInitName && d.initName) {
          d.historyInitName = d.initName;
          delete d.initName;
        }
        if (userNumZore) {
          d.userNum = 0;
        }
        for (let i = 0; i < indexArr.length; i++) {
          if (indexArr[i] !== undefined && indexArr[i] !== -1) {
            if (i === 0 && d.length >= indexArr[i] + 1) {
              d = d[indexArr[i]];
              if (removeInitName && d.initName && i != indexArr.length - 1) {
                d.historyInitName = d.initName;
                delete d.initName;
              }
              if (userNumZore && i != indexArr.length - 1) {
                d.userNum = 0;
              }
            } else if (d.innerRules.length >= indexArr[i] + 1) {
              d = d.innerRules[indexArr[i]];
              if (removeInitName && d.initName && i != indexArr.length - 1) {
                d.historyInitName = d.initName;
                delete d.initName;
              }
              if (userNumZore && i != indexArr.length - 1) {
                d.userNum = 0;
              }
            } else {
              return d;
            }
          } else {
            return d;
          }
        }
        return d;
      },
      // 根据索引号移除元素
      removeDataIndexArr(indexArr) {
        if (this.rootForm.initName) {
          this.rootForm.historyInitName = this.rootForm.initName;
          delete this.rootForm.initName;
        }
        if (this.rootForm.userNum > 0) {
          this.rootForm.userNum = 0;
        }
        let tmpIndexArr = [];
        for (let i = 0; i < indexArr.length; i++) {
          if (indexArr[i] !== undefined && indexArr[i] !== -1) {
            tmpIndexArr[i] = indexArr[i];
          } else {
            break;
          }
        }
        let len = tmpIndexArr.length;
        if (len === 1) {
          let target = this.baseDatas.splice(tmpIndexArr[0], 1)[0];
          this.updateTotalLevel(tmpIndexArr[0]);
          return target;
        }
        let children = this.baseDatas;
        delete children.initName;
        for (let j = 0; j < len - 1; j++) {
          children = j === 0 ? children[indexArr[j]] : children.innerRules[indexArr[j]];
          if (children.initName) {
            children.historyInitName = children.initName;
            delete children.initName;
          }
          if (children.userNum > 0) {
            children.userNum = 0;
          }
        }
        let target = children.innerRules.splice(tmpIndexArr[len - 1], 1)[0];

        if (children.innerRules.length === 1) {
          let bd = children.innerRules[0];
          if (bd.innerRules && bd.innerRules.length > 1) {
            children.innerRules = bd.innerRules;
          } else {
            bd.innerRules = [];
            bd.width = 210;
            this.replaceDataElement(children, bd);
          }
          this.updateTotalLevel(tmpIndexArr[0]);
        }
        this.setBaseDataWidth(tmpIndexArr[0]);
        return target;
      },
      // 根据索引号添加元素
      addDataIndexArr(indexArr, data) {
        if (!data) {
          return;
        }
        let tmpIndexArr = [];
        for (let i = 0; i < indexArr.length; i++) {
          if (indexArr[i] !== undefined && indexArr[i] !== -1) {
            tmpIndexArr[i] = indexArr[i];
          } else {
            break;
          }
        }

        if (this.rootForm.initName) {
          this.rootForm.historyInitName = this.rootForm.initName;
          delete this.rootForm.initName;
        }
        if (this.rootForm.userNum > 0) {
          this.rootForm.userNum = 0;
        }
        let len = tmpIndexArr.length;
        if (len === 1) {
          this.baseDatas.splice(tmpIndexArr[0], 0, data);
          return;
        }
        let children = this.baseDatas;
        for (let j = 0; j < len - 1; j++) {
          children = j === 0 ? children[indexArr[j]] : children.innerRules[indexArr[j]];
          if (children.initName) {
            children.historyInitName = children.initName;
            delete children.initName;
          }
          if (children.userNum > 0) {
            children.userNum = 0;
          }
        }
        if (children.innerRules.length === 0) {
          let objString = JSON.stringify(children);
          let obj2 = JSON.parse(objString);
          children.innerRules.push(obj2);
        }
        children.innerRules.splice(tmpIndexArr[len - 1], 0, data);
        this.setBaseDataWidth(tmpIndexArr[0]);
      },
      // 判断放置点的条数是否超过限制
      checkRuleLimit(indexArr) {
        let target = this.getDataIndexArr(indexArr);
        console.log(target);
        if (this.baseDatas.length > 9) {
          this.$message.warning('同层规则数请勿超过10个！');
          return false;
        }
        if (target !== undefined && target !== null && target.innerRules !== undefined && target.innerRules !== null) {
          if (target.innerRules.length > 9) {
            this.$message.warning('同层规则数请勿超过10个！');
            return false;
          }
        }
        return true;
      },
      // 根据索引号合并组合元素
      combineDataIndexArr(indexArr, data) {
        if (!data) {
          return;
        }
        let target = this.getDataIndexArr(indexArr, true, true);
        target = this.addChildren(target);
        if (target.initName) {
          target.historyInitName = target.initName;
          delete target.initName;
        }
        if (target.userNum > 0) {
          target.userNum = 0;
        }
        data.innerRules.forEach(function (el) {
          target.innerRules.push(el);
        });
        this.setBaseDataWidth(indexArr[0]);
        this.$forceUpdate();
      },
      // 设置元素的宽度
      setBaseDataWidth(index) {
        setElementWidth(this.baseDatas[index]);
      },
      // 增加元素至根节点
      addFirstData(type, moveIndexArr) {
        if (type === 'label' || this.innerConditionTypes.includes(type)) {
          this.addTagTree(this.dom, this.dragData.key);
          return;
        }
        let target = this.removeDataIndexArr(moveIndexArr);
        this.baseDatas.push(target);
        this.updateTotalLevel(this.baseDatas.length - 1);
        this.setBaseDataWidth(moveIndexArr[0]);
      },
      // 获取一个空元素
      getEmptyData(parentName, key) {
        let newNode = {
          title: parentName,
          index: 0,
          innerAndOr: '1',
          negationFlag: 0,
          width: 210,
          dataType: null,
          ruleKey: null,
          judge: 'eq',
          pastOrFuture: null,
          ruleValue1: null,
          ruleValue2: null,
          dateJudge: null,
          dateValue1: null,
          dateValue2: null,
          dateRange: null,
          relativeRange: null,
          interval: null,
          doneOrNot: "1",
          action: null,
          // 用于嵌套
          innerRules: null,
          // 用于存放单个行为的子条件
          actionInnerRules: null,
          // 行为和标签文字描述字段
          ruleDesc: parentName,
          // 行为内部规则拼接描述字段
          actionInnerRulesDesc: null,
          userNum: 0,
          notEdited: true,
          error: false,
        };
        // 行为的key中包含&分隔符
        if (key === null || (key.hasOwnProperty('type') && key.type === 'label')) {

        } else if (key.hasOwnProperty('entityId')) {
          newNode.action = this.getFirstActionByEntityId(key.entityId);
        } else if (key.indexOf("&") !== -1) {
          newNode.action = key;
        } else {
          newNode.ruleKey = key;
          let strings = key.split("_");
          if (strings[1] !== "0") {
            newNode.judge = "true";
          }
        }
        return newNode;
      },
      getFirstActionByEntityId(entityId) {
        return this.actionOptionsMap[entityId][0].itemIdentify;
      },
      getActionIconType(item) {
        let name = item.entityName;
        if (name === '交易') {
          return 'property-safety';
        }
        if (name === '权益') {
          return 'crown';
        }
        if (name === '观影') {
          return 'eye';
        }
        if (name === '搜索') {
          return 'search';
        }
        if (name === '收藏') {
          return 'book';
        }
        if (name === '曝光') {
          return 'bulb';
        }
        return 'book'
      },
      replaceDataElement(data, replaceData) {
        data.title = replaceData.title;
        data.index = replaceData.index;
        data.notEdited = replaceData.notEdited;
        data.innerAndOr = replaceData.innerAndOr;
        data.negationFlag = replaceData.negationFlag;
        data.width = replaceData.width;
        data.ruleKey = replaceData.ruleKey;
        data.ruleValue1 = replaceData.ruleValue1;
        data.ruleValue2 = replaceData.ruleValue2;
        data.judge = replaceData.judge;
        data.innerRules = replaceData.innerRules;
        data.actionInnerRules = replaceData.actionInnerRules;
        data.userNum = replaceData.userNum;
        data.dateJudge = replaceData.dateJudge;
        data.pastOrFuture = replaceData.pastOrFuture;
        data.relativeRange = replaceData.relativeRange;
        data.dataType = replaceData.dataType;
        data.interval = replaceData.interval;
        data.doneOrNot = replaceData.doneOrNot;
        data.action = replaceData.action;
        data.dateValue1 = replaceData.dateValue1;
        data.dateValue2 = replaceData.dateValue2;
        data.dateRange = replaceData.dateRange;
        data.ruleDesc = replaceData.ruleDesc;
        data.actionInnerRulesDesc = replaceData.actionInnerRulesDesc;
        data.userNumId = replaceData.userNumId;
        data.userNumLoading = replaceData.userNumLoading;
        data.error = replaceData.error;
        data.initName = replaceData.initName;
      },
      // 添加左侧标签至根节点
      addTagTree(parentName, key, code, type) {
        if (this.baseDatas.length < 10) {
          if (this.rootForm.initName) {
            this.rootForm.historyInitName = this.rootForm.initName;
            delete this.rootForm.initName;
          }
          this.rootForm.userNum = 0;
          this.baseDatas.push(this.getEmptyData(parentName, key));
          this.totalLevels[this.baseDatas.length - 1] = 1;
        } else {
          this.$message.warning('同层规则数请勿超过10个！')
        }
      },

      // 标签校验
      checkLabelAnalysis() {
        if (!this.baseRuleForm.name || !this.baseRuleForm.name.trim()) {
          this.$message.error('规则名称不能为空！');
          return false;
        }
        if (this.baseDatas.length <= 0) {
          this.$message.error('未找到可保存的标签规则信息！');
          return false;
        }
        if (JSON.stringify(this.baseDatas).length > 20000) {
          this.$message.error('规则过多，请减少规则再保存！');
          return false;
        }
        this.loading = true;
        try {
          this.passCheckedLabel = true;
          this.checkLabelData();
          if (!this.passCheckedLabel) {
            this.$message.error('标签有误，请编辑后再保存！');
          }
          return this.passCheckedLabel;
        } finally {
          this.loading = false;
        }
      },
      checkLabelData(element) {
        let bdChildren = element ? element.innerRules : this.baseDatas;
        if (bdChildren && bdChildren.length > 0) {
          for (let i = 0; i < bdChildren.length; i++) {
            this.checkLabelData(bdChildren[i]);
          }
        } else {
          this.checkElement(element);
        }
      },
      //只校验内容（不校验 innerRules 内容）
      checkElement(element) {
        let flag = this.validateElement(element);
        if (flag) {
          element.error = false;
        } else {
          element.error = true;
          this.passCheckedLabel = false;
        }
      },
      validateElement(element) {
        if (element.notEdited && element.judge !== 'true') {
          return false;
        }
        // 行为校验
        if (element.action) {

          let strings = element.action.split("&");
          if (strings.length < 4) {
            return false;
          }

          if (strings[0]) {
            let actionItem = this.actionMap[strings[0]];

            if (actionItem && !this.$refs.actionEditModal.checkItemData(element, actionItem.supportType === 'PROPORTE' ? !!element.denominatorBO : false, actionItem.supportType === 'COMPARE' ? !!element.denominatorBO : false)) {
              return false;
            }
          }

          return true;
        }
        return this.$refs.ruleEditModal.checkItemData(element);
      },
      clearRootBaseDatas(data, index) {
        this.baseDatas = JSON.parse(JSON.stringify(data.innerRules || []));
        this.rootForm = {
          innerAndOr: data.innerAndOr,
          userNum: data.userNum,
          innerRules: [],
          boIndex: index || 0
        };
        this.updateTotalLevel();
        this.$forceUpdate();
      },
      clearFields() {
        this.clearRootBaseDatas({innerAndOr: '1', userNum: 0});
        this.passCheckedLabel = true;
      },
      initData() {
        this.clearFields();
      },

      // 筛选配置校验，指标校验
      validatorData() {
        let len = this.baseRuleFrontBONames.length;
        for (let i = 0; i < len; i++) {
          let el = this.baseRuleFrontBOList[i];
          if (el.innerRules.length <= 0) {
            let messageTitle = this.messageTitle || ('【筛选-' + this.baseRuleFrontBONames[i] + '】');
            this.$message.error(messageTitle + '配置不能为空，请调整！')
            return false;
          }
          this.passCheckedLabel = true;
          this.checkLabelData(el);
          if (!this.passCheckedLabel) {
            this.choiceShowMessage(i, el);
            let messageTitle = this.messageTitle || ('【筛选-' + this.baseRuleFrontBONames[i] + '】');
            this.$message.error(messageTitle + '配置有误，请编辑后再试！');
            return false;
          }
        }
        return true;
      },
      choiceShowMessage(index, el) {
        if (this.rootForm.boIndex === index) {
          el.innerRules.forEach((p, index) => {
            this.baseDatas[index].error = p.error || false;
          });
        }
      },
      gainRuleModel() {
        let ruleModel = {};
        this.baseRuleFrontBONames.forEach((p, index) => {
          let ruleModelTmp = {};
          let data = this.baseRuleFrontBOList[index];
          if (data.innerRules.length === 1) {
            ruleModelTmp = data.innerRules[0];
          } else {
            ruleModelTmp = data;
          }

          if (!ruleModelTmp.initName) {
            ruleModelTmp.initName = 'label' + this.userId + Date.now() + index;
            if (this.rootForm.boIndex === index) {
              if (data.innerRules.length === 1) {
                this.baseDatas[0].initName = ruleModelTmp.initName;
              } else {
                this.rootForm.initName = ruleModelTmp.initName;
              }
            }
          }
          ruleModel[p] = ruleModelTmp;
        });
        return ruleModel;
      },
      gainBaseDatas() {
        return this.baseDatas;
      },
      gainRootForm() {
        return this.rootForm
      },
      gainLabelKeyMap(){
        return this.labelKeyMap;
      },
      gainActionMap(){
        return this.actionMap
      }
    },
    computed: {
      userId() {
        return this.$store.getters.userInfo.id;
      },
      contentParentStyle() {
        let style = {
          'box-shadow': '0 2px 8px rgba(0,0,0,.18)',
          'border-color': 'transparent',
          position: 'relative',
          'z-index': 1
        };
        return style;
      },
    },
    watch: {}
  }
</script>
