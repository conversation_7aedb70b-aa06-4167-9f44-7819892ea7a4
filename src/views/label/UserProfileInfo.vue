<template>
  <div style="position: relative;">
    <a-button :class="{'upi-ul-btn-': hideTypeTitle !== '详情', upiUlBtnDefault: true}" type="primary"
              :shape="hideTypeTitle === '详情'? undefined: 'round'"
              :icon="hideTypeTitle === '详情'? 'arrow-left': 'arrow-right'"
              @click="changeHideTypeTitle">
      查看{{hideTypeTitle}}
    </a-button>
    <div class="page-header-index-wide user-profile-info" style="min-width: 1000px;">
      <div style="width: 100%;">
        <div style="width: inherit; overflow: hidden; position: relative;">
          <ul class="list" :style="{ transform: 'translate('+(hideTypeTitle === '详情'? '0':'-50%') + ')'}"
              style="width: 200%; position: relative;transition: transform 500ms ease 0s">
            <li class="item" style="width: 50%;float: left;">
              <user-portrait ref="userPortrait" :gid="userInfo.gid"
                             :user-type="userInfo.sex === '2' || userInfo.sex === '女'? 'w':'m'"
                             :user-type-name="userType" :user-info="userInfo" :right-content-width="contentWidth"/>
            </li>
            <li class="item" style="width: 50%;float: left;">
              <a-row>
                <a-col :span="6">
                  <a-row ref="user-profile-info-aside" :style="asideFlexStyle">
                    <a-card :bordered="false" style="margin-bottom: 10px; cursor: default;" hoverable>
                      <div>
                        <div class="account-center-avatarHolder" v-if="ugsType !== 'user' && ugsType !== 'device'">
                          <span class="seal-cancel" v-if="userBusiness.businessState === 'CANCEL'">已注销</span>
                          <a-avatar :size="64"
                                    :class="{gray: false && userBusiness.businessState === 'CANCEL'}"
                                    :src="!userInfo.faceImg? '/view/default.jpg' : userInfo.faceImg"/>
                          <div class="info">
                            <div class="username">
                              <a-icon style="color: #00DB00" v-if="userInfo.sex === '1' || userInfo.sex === '男'"
                                      type="man"/>
                              <a-icon style="color: #db3e30" v-else-if="userInfo.sex === '2' || userInfo.sex === '女'"
                                      type="woman"/>
                              <a-icon style="color: #00DB00" v-else type="man"/>&nbsp;
                              {{ getRealName(userAccountInfo.realName) }}
                            </div>
                            <div class="gid">
                              <a-icon style="color: #00DB00" type="smile"/>&nbsp;
                              ID:&nbsp;{{ userInfo.gid }}
                            </div>
                          </div>
                        </div>
                        <div class="account-center-detail">
                          <p v-if="ugsType === 'user' || ugsType === 'device'">
                            <a-icon style="color: #00DB00" type="smile"/>
                            ID:&nbsp;{{ userInfo.gid }}
                          </p>
                          <p>
                            <address-icon/>
                            <span>{{ userAccountInfo.cityCode == null || userAccountInfo.cityCode == '' ? '暂无' : userAccountInfo.cityCode }}</span>
                          </p>
                        </div>
                      </div>
                    </a-card>
                    <a-card :bordered="false" style="margin-bottom: 10px; cursor: default;" hoverable
                            :bodyStyle="{paddingRight: 0}">
                      <div slot="title">
                        <a-avatar size="small" icon="tags" style="color: orange; background-color: #fff6dc"/>
                        <span class="ivu-pl-8">事实标签</span>
                      </div>
                      <div class="account-center-tags">
                        <div class="label-model">
                          <div>
                            <template v-for="tag in factsLabel">
                              <a-tag :key="tag.labelCode" color="orange">
                                {{ tag.labelName }}
                              </a-tag>
                            </template>
                            <span v-if="!factsLabel || factsLabel.length <= 0" style="color: #999;">
                    暂无
                  </span>
                          </div>
                        </div>
                      </div>
                      <ugs-spin fix v-if="spinLoadings.includes('u1-tag')"/>
                    </a-card>
                    <a-card :bordered="false" style="margin-bottom: 10px; cursor: default;" hoverable
                            :bodyStyle="{paddingRight: 0}">
                      <div slot="title">
                        <a-avatar size="small" icon="tags" style="color: #1890ff; background-color: #e6f7ff"/>
                        <span class="ivu-pl-8">模型标签</span>
                      </div>
                      <div class="account-center-tags">
                        <div class="label-model">
                          <div>
                            <template v-for="tag1 in modelLabel">
                              <a-tag :key="tag1.labelCode" color="blue">
                                {{ tag1.labelName }}
                              </a-tag>
                            </template>
                            <span v-if="!modelLabel || modelLabel.length <= 0" style="color: #999;">
                    暂无
                  </span>
                          </div>
                        </div>
                      </div>
                      <ugs-spin fix v-if="spinLoadings.includes('u1-tag')"/>
                    </a-card>
                    <a-card :bordered="false" style="margin-bottom: 10px; cursor: default;" hoverable
                            :bodyStyle="{paddingRight: 0}">
                      <div slot="title">
                        <a-avatar size="small" icon="tags" style="color: #52c41a; background-color: #f6ffed"/>
                        <span class="ivu-pl-8">外部标签</span>
                      </div>
                      <div class="account-center-tags">
                        <div class="label-model">
                          <div>
                            <template v-for="tag2 in bigDataLabel">
                              <a-tag :key="tag2.labelCode" color="cyan">
                                {{ tag2.labelName }}
                              </a-tag>
                            </template>
                            <span v-if="!bigDataLabel || bigDataLabel.length <= 0" style="color: #999;">
                  暂无
                  </span>
                          </div>
                        </div>
                      </div>
                      <ugs-spin fix v-if="spinLoadings.includes('u1-tag')"/>
                    </a-card>
                  </a-row>
                </a-col>
                <a-col :span="18">
                  <div class="right-info">
                    <a-row>
                      <a-col :sm="24" :md="12" :xl="8">
                        <a-card :bodyStyle="{padding:'12px'}" style="margin-right: 10px; cursor: default;" hoverable>
                          <p style="margin-bottom: 1em;" class="ugs-ell">
                            <a-icon type="bank" theme="twoTone" style="margin-right: 6px;font-size: 16px;"/>
                            开户时间&emsp;<span
                            style="margin-right: 6px;font-size: 16px;">{{ this.getCreateTime(userBusiness) }}</span>
                          </p>
                          <p style="margin-bottom: 0; ">
                            <a-icon type="fire" theme="twoTone" style="margin-right: 6px;font-size: 16px;"/>
                            客户状态&emsp;
                            <span style="margin-right: 6px;font-size: 16px;font-weight: 700;padding-left: 15px">{{userType.businessState[userBusiness.businessState] || '正常'}}</span>
                          </p>
                        </a-card>
                      </a-col>
                      <a-col :sm="24" :md="12" :xl="16">
                        <a-card :bodyStyle="{padding:'12px'}" style="cursor: default;" hoverable>
                          <a-row>
                            <a-col :span="16" style="width: calc(100% - 180px);">
                              <ul class="head-overall">
                                <li>
                                  <div class="num">{{userInfoExtend.isPay}}</div>
                                  <div class="name">付费用户</div>
                                </li>
                                <li>
                                  <div class="num">{{userInfoExtend.lastViewDay}}</div>
                                  <div class="name">{{userInfoExtend.lastViewDay === '今天'? '最后观影日期' : '最后观影距离当前'}}</div>
                                </li>
                                <li>
                                  <div class="num">{{ Number(JSON.stringify(userInfo) === '{}' ? 0 : factsLabel.length)
                                    + Number(JSON.stringify(userInfo) === '{}' ? 0 : (modelLabel.length +
                                    bigDataLabel.length)) }}
                                  </div>
                                  <div class="name">全部标签数</div>
                                </li>
                              </ul>
                            </a-col>
                            <a-col :span="8" style="width: 180px; text-align: right;">
                              <p style="margin-bottom: 1em;" class="ugs-ell">
                                <a-icon type="tag" theme="twoTone" style="margin-right: 6px;font-size: 16px;"
                                        twoToneColor="#fa8c16"/>
                                事实标签数&emsp;
                                <span
                                  style="margin-right: 6px;font-size: 16px;font-weight: 700;padding-left: 15px; display: inline-block; width:50px;text-align: left;">{{ factsLabel.length || 0 }}</span>
                              </p>
                              <p style="margin-bottom: 0; ">
                                <a-icon type="tag" theme="twoTone" style="margin-right: 6px;font-size: 16px;"/>
                                模型标签数&emsp;
                                <span
                                  style="margin-right: 6px;font-size: 16px;font-weight: 700;padding-left: 15px; display: inline-block; width:50px;text-align: left;">{{ modelLabel.length || 0 }}</span>
                              </p>
                            </a-col>
                          </a-row>
                          <ugs-spin fix
                                    v-if="spinLoadings.includes('u1-tag') || spinLoadings.includes('u1-info-extend') "/>
                        </a-card>
                      </a-col>
                    </a-row>

                    <a-row>
                      <a-card :body-style="{padding: '0'}" style="margin-top:10px;cursor: default;" hoverable>
                        <div class="salesCard">
                          <a-tabs defaultActiveKey="1"
                                  :tabBarStyle="{'margin-top': '-4px', paddingLeft: '16px', 'background-image': 'linear-gradient(to right, #1890ff0a, #C87AE990, #ff68ac2f, #ff7f6690, #f6ab2e0a)'}">
                            <a-tab-pane key="1" style="margin: 0; padding:0 12px">
                      <span slot="tab">
                        <a-icon type="user"/>
                        用户信息
                      </span>
                              <div style="height: 40px; margin-bottom: 12px;"
                                   v-if="ugsType !== 'user' && ugsType !== 'device'">
                                <div class="userTopImg" style="border-radius:20px;margin:0 auto;">
                                  <div style="margin-left: 20%;font-size: 25px;color: #cc38a8;font-family: Apple">
                                    <div style="float: left">已加入移动大家庭</div>
                                    <div v-if="openTimeStr.length >= 1"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(0, 1) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 2"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(1, 2) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 3"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(2, 3) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 4"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(3, 4) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 5"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(4, 5) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 6"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(5, 6) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 7"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(6, 7) }}
                                    </div>
                                    <div v-if="openTimeStr.length >= 8"
                                         style="float: left;margin-left: 1%;background-color: rgb(245,245,245);border-radius:10px;height: 30px;width: 30px;text-align: center;">
                                      {{ openTimeStr.substring(7, 8) }}
                                    </div>
                                    <div style="float: left;margin-left: 1%">天啦！</div>
                                  </div>
                                </div>
                              </div>

                              <a-card title="用户信息"
                                      style="border-radius:20px; margin-bottom: 12px;cursor: default;" hoverable
                                      :head-style="{'background-color': 'rgba(219, 225, 241, 0.5)', 'border-radius':'20px 20px 0 0'}">
                                <template>
                                  <a-row :gutter="1">
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;">
                                      姓名：{{ getRealName(userAccountInfo.realName) }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;" v-if="userBusiness">
                                      客户类型：{{ getBusinessType(userBusiness) }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.provinceCode">
                                      省份：{{ userAccountInfo.provinceCode }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.serviceChannelId">
                                      业务渠道：{{ userBusiness.serviceChannelId }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.birth">
                                      生日：{{ userAccountInfo.birth }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.ageGroup">
                                      年龄段：{{ userAccountInfo.ageGroup }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.hobbies">
                                      观看喜好：{{ userAccountInfo.hobbies }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.starHobbies">
                                      明星喜好：{{ userAccountInfo.starHobbies }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.startDate">
                                      服务开始时间：{{ userBusiness.startDate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.endDate">
                                      服务结束时间：{{ userBusiness.endDate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.openTime">
                                      开户时间：{{ userBusiness.openTime }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.activateDate">
                                      激活时间：{{ userBusiness.activateDate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.cancelledDate">
                                      注销时间：{{ userBusiness.cancelledDate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.licence">
                                      牌照方：{{ userBusiness.licence }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userAccountInfo.identity">
                                      身份：{{ userAccountInfo.identity }}
                                    </a-col>
                                    <!--<a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;" v-if="userBusiness.cdnType">-->
                                    <!--CDN类型：{{ userBusiness.cdnType }}-->
                                    <!--</a-col>-->
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.groupId">
                                      集团标识：{{ userBusiness.groupId }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.billId">
                                      业务账号：{{ userBusiness.billId }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.bandwidth">
                                      宽带速率：{{ userBusiness.bandwidth }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.bandEndDate">
                                      宽带到期时间：{{ userBusiness.bandEndDate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.platform">
                                      平台类型：{{ userBusiness.platform }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.bitrate">
                                      视频码率：{{ userBusiness.bitrate }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.faceCode">
                                      人脸识别号：{{ userBusiness.faceCode }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.source">
                                      用户创建来源：{{ userBusiness.source }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.createTime">
                                      用户创建时间：{{ userBusiness.createTime }}
                                    </a-col>
                                    <a-col :lg="12" :md="12" :sm="24" style="margin-bottom: 1em;"
                                           v-if="userBusiness.updateTime">
                                      用户更新时间：{{ userBusiness.updateTime }}
                                    </a-col>
                                  </a-row>
                                  <ugs-spin style="border-radius: 20px;" fix
                                            v-if="spinLoadings.includes('u1-info') || spinLoadings.includes('u1-business')"/>
                                </template>
                              </a-card>

                              <a-card title="用户特征"
                                      style="border-radius:20px; margin-bottom: 12px;cursor: default;" hoverable
                                      :head-style="{'background-color': 'rgba(219, 225, 241, 0.5)', 'border-radius':'20px 20px 0 0'}"
                                      :body-style="{overflow: 'hidden'}">
                                <div style="min-height: 24px;">
                                  <features-modal :gid="userInfo.gid" :width="rightContentWidth + 34"
                                                  @loadingStatus="featuresLoadingStatus"/>
                                </div>
                                <ugs-spin style="border-radius: 20px;" fix v-if="spinLoadings.includes('u1-features')"/>
                              </a-card>

                              <a-card title="客户状态"
                                      style="border-radius:20px; margin-bottom: 12px;cursor: default;" hoverable
                                      :head-style="{'background-color': 'rgba(219, 225, 241, 0.5)', 'border-radius':'20px 20px 0 0'}">
                                <fish-bone :has-desc="false" :data-array="userAccountByTab"
                                           :total="userAccountByTab.length"></fish-bone>
                                <ugs-spin style="border-radius: 20px;" fix v-if="spinLoadings.includes('u1-info-tab')"/>
                              </a-card>
                            </a-tab-pane>
                            <a-tab-pane key="2" style="margin: 0; padding:0 12px"
                                        v-if="viewAtlasType === 'ub' || viewAtlasType === 'yzh'">
                              <span slot="tab"><a-icon type="eye"/>观影信息</span>
                              <view-tab :gid="userInfo.gid"/>
                            </a-tab-pane>
                            <a-tab-pane key="6" style="margin: 0; padding:0 12px" v-if="viewAtlasType === 'yzh'">
                              <span slot="tab"><a-icon type="eye"/>收视图谱</span>
                              <view-map-tab :device-list="deviceList"/>
                            </a-tab-pane>
                            <a-tab-pane key="7" style="margin: 0; padding:0 12px" v-if="viewAtlasType === 'yzh'">
                              <span slot="tab"><a-icon type="eye"/>家庭设备</span>
                              <family-view-map-tab :device-list="deviceList"/>
                            </a-tab-pane>
                            <a-tab-pane key="8" style="margin: 0; padding:0 12px" v-if="viewAtlasType === 'ugs'">
                              <span slot="tab"><a-icon type="eye"/>收视图谱</span>
                              <view-altas-map-tab :gid="userInfo.gid"/>
                            </a-tab-pane>

                            <a-tab-pane key="9" style="margin: 0; padding:0 12px" v-if="viewAtlasType === 'ugs'">
                              <span slot="tab"><a-icon type="eye"/>用户路径</span>
                              <way-tab :gid="userInfo.gid"/>
                            </a-tab-pane>

                            <a-tab-pane key="3" style="margin: 0; padding:0 12px">
                              <span slot="tab"><a-icon type="star"/>收藏信息</span>
                              <store-tab :gid="userInfo.gid" :right-content-width="rightContentWidth"/>
                            </a-tab-pane>
                            <a-tab-pane key="4" style="margin: 0; padding:0 12px;">
                              <span slot="tab"><a-icon type="property-safety"/>消费信息</span>
                              <paid-tab :gid="userInfo.gid" :right-content-width="rightContentWidth"/>
                            </a-tab-pane>
                            <a-tab-pane key="5" style="margin: 0; padding:0 12px;">
                              <span slot="tab"><a-icon type="laptop"/>设备信息</span>
                              <device-tab :gid="userInfo.gid" :right-content-width="rightContentWidth"/>
                            </a-tab-pane>

                          </a-tabs>
                        </div>
                      </a-card>
                    </a-row>
                  </div>
                </a-col>
              </a-row>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <a-back-top/>
  </div>
</template>

<script>
  import ChartCard from '@/components/ChartCard'
  import ACol from "ant-design-vue/es/grid/Col"
  import ATooltip from "ant-design-vue/es/tooltip/Tooltip"
  import ABackTop from 'ant-design-vue/es/back-top/index'
  import ACard from "ant-design-vue/es/card/Card"
  import MiniArea from '@/components/chart/MiniArea'
  import MiniBar from '@/components/chart/MiniBar'
  import MiniProgress from '@/components/chart/MiniProgress'
  import RankList from '@/components/chart/RankList'
  import Bar from '@/components/chart/Bar'
  import LineChartMultid from '@/components/chart/LineChartMultid'
  import HeadInfo from '@/components/tools/HeadInfo.vue'
  import AreaChartTy from '@/components/chart/AreaChartTy'


  import Trend from '@/components/Trend'
  import {getLoginfo, getVisitInfo} from '@/api/api'
  import {getAction} from '@/api/manage'
  import {mapGetters} from 'vuex'
  import FishBone from "../main-components/fishBone/index";
  import LeftTagNav from "../main-components/nav/leftTagNav/index";
  import ViewTimeline from "./modules/viewTimeline";
  import ProgressBarList from "../main-components/progressBar/ProgressBarList";
  import ViewAtlasAnalysis from "./user/analysis/ViewAtlasAnalysis";
  import ViewMapTab from "./user/profile/ViewMapTab";
  import FamilyViewMapTab from "./user/profile/FamilyViewMapTab";
  import ViewAltasMapTab from "./user/profile/ViewAtlasMapTab";
  import ViewTab from "./user/profile/ViewTab";
  import WayTab from "./user/profile/WayTab";
  import StoreTab from "./user/profile/StoreTab";
  import PaidTab from "./user/profile/PaidTab";
  import DeviceTab from "./user/profile/DeviceTab";
  import FeaturesModal from "./user/profile/FeaturesModal";
  import {mixin} from '@/utils/mixin.js'
  import UgsSpin from "../modules/spin/UgsSpin";
  import UserPortrait from "./user/profile/UserPortrait";

  const rankList = []
  const rankList2 = []

  const AddressSvg = {
    template: `<div><?xml version="1.0" encoding="UTF-8" standalone="no"?>
      <!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
      <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px" y="0px" width="64px" height="64px" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve">
      <image id="image0" width="14" height="14" x="0" y="0"
          href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAACBjSFJN
      AAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QA/wD/AP+gvaeTAAAA
      B3RJTUUH5AYZACgAvg2wKwAADJ5JREFUeNrlm3mMXMWdxz9V73X3TM/h8Rw+ZuwZbGyMOYzHHEbE
      BhvbCznYFbtCaI+gbAQbbbTKHspq8W40HCaLl1WilREKC4RoIUqikEQbwGwIAQziCBjLcSA2eMYO
      M/Z4xmZOz9mvu+u3f/T0zOvu916/7hm0f2xJP3VXddWv6vutX/3qV/VeKxHh/3Oyw1bsnWJF2uIJ
      FNuA2EJ07qZeLSwuB+GAnebO5kpOBVVUYS2gJ8kvgV3zBRpUpkKWhU0KDqyMsD2oTmgLAK4uB7An
      ePFvp5RHWZmECFxerI4Oq0zBb8IALxDxEAIkZP1QBAgHF4wAK8UdCC8CiaLAw4INKwH6fNK0Ev7H
      SnNn0Ymdzy5wFKJVKb4uwjdnGM8hxf2pJxNUfvAxsa5eomcGsIbG0JMZLk08Rrq+Bqe5kem1LUxf
      egEmPudnVd4nzC0VNVfejeLOcZvXLwEnLIZ5EQDwEdREHc67wbq/W0PnqXvhXeKHjqNS6VA6JWIx
      uXENozuvJNnc4E2AiwiVKbixzebVUsc/bwuodPhH4IH8GVfTDnXPvUX1Wx+EBl44OsXYZy5j5I+2
      YCqiBUS4SUhEqF0HY+USYPU4dCjFlwRagxq41152TbqBC2B/MsKSx58j0jdYNrnulFyymHN/dQup
      pfXeBADa4gttFvvLIqDH4UEUdxer7HZKBWTMfMY6T7P0iefQk9MLAj6b0vEKzt31h0yvafEkQCnO
      ivDVqigvLoWJ0ghIMgA0zAe8AJGzQyz/9o+wFhh8NpmKKL1//6ekmhvmgLtJyH5XuXmPNIzwaGuU
      jiwBw0BdqeDdBOjxKVq+9QPsgZFPBXw2JZvqOPMPf066qmLOAboIKIEERPimnkHwnfmAF4GGn7yM
      PTDMPHb7UBL5ZJjFz72eBVBghfn+KcjFK8Vf2wCtUTp6HCwUXwEW5YPHr5OZjmIne6l+71ioGVQt
      TaitG1AXt6IaMl3J4ChyrBt547dI70BRHbVv/5bRGzbhLG+cjZVV3lizMy95+bxkQm2DJxOsRfOI
      CLvywQuw/NGfEP/diWAltoW+fQf6+o2FAf/scATz2mHMM69Aka1zYsNa+u/641lwfsshcCkIe0OF
      wqtjdKo0d821mwNvj44RP3aCQNO1Ndbf3oa+od0fPIBW6O2bsL52G9g6UGfV+51YI2MF48kZIz5L
      QdGrhD2tUb4R+jTogLY8OosfOQ7GBLbVt+9ErWsL2xXq4jb0bTdifvhL/0oiVH7QxdiW9jnzFxDF
      L3Sau9ZUcjpMX6Es4PgULZbmEbczzLJc2dWDQnxFtzRlzL7EpG/YhG5uDNQd7+zGkDu7pYD3JeDU
      JM0fO+w/6TB50kEsi9PAZ93AsxLtO0eQqaqtVwSbve/IFGrLhkDdsf5zBSfEkrvxKkzbfFfgc0Bl
      wbE0Lx8ZOR9MwPpVZQxrJq1fHajbHj5fMK605rETU6wM24WnDxDYXKxhtkPlJIAAH1BfWzb+zDbp
      r1snpnNmfsbOPpvW9HQ6oMBB8ZpOc+fqCno8dfige9/zUsIjb2IRgmapLLssoNlb0pWxYuOLIuwy
      Fk/4kujT7x0ofgUkiw3LWdIQPNDh0fLhD44G6naW1IfyAUr8LdqTgFUVdK+OsOvCKNE1UVQySgzN
      NQL788PiidUryJipt8jRrrIJ4GhXoO6JVStmxwH+VirwdkkE5KdLwLkowsGLY3xByJy5sx2Mbrw0
      cJbkjUNF4wTPZAzmjUOBuofbLyuIS/KsYAp4Pp3iy/MiIHdg3O82t4kLVpJoWuw7S9Lbh7z2bsnd
      yIF3oK/fV2+isY6pFc35hyFHGf5JpWlZF0NdFCO+Nsot6+KcWTACrEqOZXvMdj6y8TIUxlfkmeeR
      D0+E7kOOdSHP7A/UObxpAzITX8xGgULH+koeCgI8bwJSk1ycH3MPbLkWCYrdUylk35PIq28FLwdj
      kFfeRPZ9D9IpX33GtvjkM5sLHF9KeLpUPKU8GcoA1tybBZ5l3llUy+CVG2l8J+A5RMogP/xv5LW3
      UVuuQa1fC42ZOz4GhpCjx5E3DkLf2aJjGLymHWdRbWb2MvE/AlSUsemGIuAoRO0kG1OGe3GFxO7U
      v2Mbje8ehGLH6zP9yI+fLTs8EK3p236DN8eKLwIPzYuA7kmWpy32GMVOgTYRsPCPx7JAppuaGLri
      cuoP/6ZMaOHSYHs7042N6JmLkJmPTFLc/7spsIXvh/UDORcip6docSwOCiz3irAMYLIRoBTmI4ND
      XP7gXnQq9amANxGbI7t3k6xfjAb0zGWHVnjmXZci54GXLeHu1TGOu3XmOEHH4mFgebkDTDTUc3bL
      dQQFL/ORvq1bSdQvLmdotQpuFcWvTya4yJcABTtK1azyvpy56SZSVXEW+jI0VVnJmR07Cvor8aC9
      GMWDvgSUBNjnt1RlJaduvpmFnv2ez3+OVDweagxF0o3uTI4TVJqXMdwK3vuJArqd0/x86Bf0OKdI
      GO+HsKoN9i3TrO8v85lgXvpwicXfLNuPOen95Cuio2xbfB23NNwcSMKMT8j5OccCJM1uYNhVOecy
      p9fpZ1//43ROn/AFD5l9+eGdMYye/8wbZfj29ihp8H55QsBJO7w0cICfD75QCLrwVvglXwJWxfhI
      hGuBn0HmkbebjF+NvI5jwj16P77U4tkrosx37f9sQ4TORu2PfkZEhAODb3o+KpudGDijU3zNXVYQ
      B8xsE3/iBerd8cP9wNJQDACPb6lg8+8TLB8p4zQI9Ndonrw6hoRsnjQpjk11cml8bU65go8RXrJS
      dLRV0Z/zW9j3A7Z/uPsCZfh9qSDaTyX595+eR5UY+omCu2+t5b22iH8dAXFMDkHKVj8+cMXe28P2
      E34XSMu1xczQSw6vsHnh0iilrv3nL4/yXqsdqFsh6EhecVq2lkJ0eAIMm8vAjwg8urWKs7WEBt9f
      o/jP6+Ih9auZsDQjkpZlVx36SnzhCUA2l+vLJiKKf72pFqOKgxdl+Lc/qGEyqovqFQPpaZNLiEFV
      mrovhUU16wQPQaTJ4X7R3CFCs/sMkDAOzVXLfPfXtDFMpacYTYz7dvT+sig/uKqKvzjoXwfg6auq
      OdIc9QxEBBBHECOBjnFd9apHOh0eyXlICiiFo+C4gn2tEb4LmFkLaHJ4AMXdCM1uZQo4MnEMFWCH
      llJU23FqI9WB5vq9TVUcbcqeLQvlo0aL/2r315GeMqQdwaQys+8n3eO9BS9MzKQocBnwWE+SF7sg
      5l4Cd0DhY2WAYxNdgR1mJa4rfNGblMFJwP3X1zEZkQLw07Zw3/V1OElBkqawfUIQ/0uiHJlyEgwl
      R3Im0SMg2hlL8fU5ArT3kV8BfYlz4RwS4jsoM50x3dNVFg9ftQglJkf+4+paeqotJC2kE4XtvUgJ
      klc/eSvMKzJ/OUeAmbtPyw+Dx81kOGfnJHzH5DbbZy+M82h7LefimrNViu+01/L8hVWzBiEpKWyf
      Lm6Bbjk8/OHcJPq/JLFy1gmOR+modgDNFxGas5X7kwOZ7SWAybQYplLTjCX9HZwi13E9dUkNT11S
      M1dg5npQ2ftVd3tF6IgQoG/8XPE3RKCnaCR4y5E9e6oi8W+4y8aS40wkJ0u61zNJITURAoECO67R
      kdwhG0dIO6WFk/ds+Crralb5EiCKjqJxgMbanu+wx52pzFVYCaJshY4pRLLb2Nx2NisCOqZRtips
      H1EoJTlBTzF55dyvg2b/zaTNQ0UJsLDW5w+myqoM3BZ9t8uYIlJtZWZXZaI4EUBlQtpIjcaK4t++
      UqMiGUhhujw61JUPPgV0iqJDR9i1BhKB1+Lbuc9uUPHF+cuk2q6i2q4KJE4QHOMw6oyRdi9eDVZc
      Y/m1K2LlOqoyu3mIu58RxobaIqpRArQGWkDloVSbNkqVc5ehjCJGjEV2bVnh8wJJ/bYDf7cokNCg
      H6eutLuNiCn3ECQCtrL/Lwk4e2Bb3fkgjIEEvMo9qalk4qe5Tqs0SaZSZZM3bzHqXuGewK0n1IXI
      59954MmojvyZpVQs7EW0AEmTZDR1PtcHfMpJK01MR0+B2vPStfc9Xqy+FwF2T4p/Rvgy0BYGaFZD
      /juE7k988p6D8skX+c9QMaVnMDw9HqXD/Z+iAgK6HR5Qin8Jo9MPrBcRYUko9udJN3C/+kUGu7c1
      ym5fAnoc+lAsK1WvFxH5ZJRNgCr8vex/lCp6W21WZLOFcYDGlPvsWoUtDNDv9VJpuf8c9Uwm1/Fr
      jwpPzbcP92WEzstnn94qD9HKo25efgHSY+5MgQWMR7nHdSpsWYge/da1FKmzwOljpXhiZYS97sL/
      BTFlW+FXe880AAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIwLTA2LTI0VDE2OjQwOjAwKzA4OjAw2crU
      ZwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMC0wNi0yNFQxNjo0MDowMCswODowMKiXbNsAAAAgdEVY
      dHNvZnR3YXJlAGh0dHBzOi8vaW1hZ2VtYWdpY2sub3JnvM8dnQAAABh0RVh0VGh1bWI6OkRvY3Vt
      ZW50OjpQYWdlcwAxp/+7LwAAABd0RVh0VGh1bWI6OkltYWdlOjpIZWlnaHQANjS84KmEAAAAFnRF
      WHRUaHVtYjo6SW1hZ2U6OldpZHRoADY0RE9pCQAAABl0RVh0VGh1bWI6Ok1pbWV0eXBlAGltYWdl
      L3BuZz+yVk4AAAAXdEVYdFRodW1iOjpNVGltZQAxNTkyOTg4MDAwYZl7QgAAABJ0RVh0VGh1bWI6
      OlNpemUAMzM3NEJCQZyC8wAAAEJ0RVh0VGh1bWI6OlVSSQBmaWxlOi8vL3RtcC9pbWFnZWxjL2lt
      Z3ZpZXcyXzdfMTU5MjUzMjg1ODU4NDQyNjdfMTZfWzBdGAmsBwAAAABJRU5ErkJggg==" ></image>
      </svg>
      </div>`
  };

  const AddressIcon = {
    template: `
    <a-icon :component="AddressSvg" />
  `,
    data() {
      return {
        AddressSvg,
      };
    },
  };


  export default {
    name: "UserProfileInfo",
    components: {
      UserPortrait,
      UgsSpin,
      FeaturesModal,
      DeviceTab,
      PaidTab,
      StoreTab,
      ViewTab,
      WayTab,
      ViewAltasMapTab,
      FamilyViewMapTab,
      ViewMapTab,
      ViewAtlasAnalysis,
      ProgressBarList,
      ViewTimeline,
      LeftTagNav,
      FishBone,
      ATooltip,
      ABackTop,
      ACard,
      ACol,
      ChartCard,
      MiniArea,
      MiniBar,
      MiniProgress,
      RankList,
      Bar,
      Trend,
      LineChartMultid,
      HeadInfo,
      AddressIcon,
      AreaChartTy
    },
    mixins: [mixin],
    data() {
      return {
        hideTypeTitle: '详情',
        asideFlexStyle: '',
        height: 120,
        userInfo: {},
        userInfoExtend: {
          isPay: '--',
          lastViewDay: '--'
        },
        userType: {
          businessState: {
            CANCEL: '已注销',
            UNUSABLE: '暂停',
            NORMAL: '正常'
          }
        },
        userBusiness: {},
        openTimeStr: '',
        activeUserData: [],
        userAccountByTab: [],
        paidCount: 0,
        userAccountInfo: {},
        screenHeight: document.body.clientHeight * 0.7,

        deviceList: [],
        url: {
          findUserAccountInfoByGid: "/userProfile/getUserAccountInfoByGid",
          findLabelByGid: '/user/findLabelByGid',
          // findCityNameByCityCode: '/user/findCityNameByCityCode',
          findUserInfoByGid: '/user/findUserInfoByGid',
          findUserBusinessByGid: '/user/findUserBusinessByGid',

          findDevicesByGid: '/viewMap/findDevicesByGid',

          findIsPaidUser: '/user/findIsPaidUser',
          findLastDateByGid: '/user/findLastDateByGid',
        },
        factsLabel: [],
        modelLabel: [],
        bigDataLabel: [],

        requestTimes: -1,

        // cityName: '',
        userInfoVO: [],
        tagInputVisible: false,
        tagInputValue: '',
        teams: [],
        teamSpinning: true,
        effectiveLabelCount: "0",
        loading: false,
        spinLoadings: [],
        center: null,
        rankList,
        rankList2,
        loginfo: {},
        visitFields: ['ip', 'visit'],
        visitInfo: [],
        pieRankList: [],
        averageAgeUserNum: [],
        indicator: '< a - icon type = "loading" style = "font-size: 24px" spin / >',
      }
    },
    computed: {
      contentWidth: vm => vm.$store.state.app.windowWidth - 40,
      leftContentWidth: vm => (vm.$store.state.app.windowWidth - 40) * 6 / 24 - 68,
      rightContentWidth: vm => (vm.$store.state.app.windowWidth - 40) * 17 / 24 - 68,
      ugsType: vm => window._CONFIG['ugsType'],
      viewAtlasType() {
        let tmp = this.$store.state.app.sysConfig.viewAtlasProvince;
        if (tmp) {
          if (tmp === '1') {
            return 'ugs'
          } else if (tmp === '2') {
            return 'ub'
          } else if (tmp.trim().length > 0) {
            return 'yzh';
          }
        }
        return 'ub';
      }
    },
    created() {
      this.initData();
    },
    mounted() {
      this.getTeams();
    },
    beforeDestroy() {
    },
    methods: {
      handleWidthChange() {
        this.asideFlexStyle = '';
      },
      initData() {
        //父页面传过来的用户信息
        this.userInfo = this.$route.query.userInfo;
        if (!this.userInfo) {
          this.userInfo = {};
          return;
        }
        delete this.$route.query.reload;

        this.requestTimes = 6;
        this.loading = true;

        // 用户信息tab、全局
        this.findUserBusiness(this.userInfo);
        this.loadUserAccountInfoByGid(this.userInfo)
        this.findUserInfoByGid(this.userInfo);

        // 左侧标签信息分组
        this.getUserLabels(this.userInfo)
        // this.findCityNameByCityCode(this.userInfo)

        // 加载收视图谱
        if (this.viewAtlasType === 'yzh') {
          this.findDevicesByGid();
        }

        // 右侧顶部
        this.findIsPaidUser();
        this.findLastDateByGid();
      },
      subTimes(key) {
        this.requestTimes--;
        if (this.requestTimes <= 0) {
          this.loading = false;
        }
        if (key) {
          this.spinLoadings.splice(this.spinLoadings.indexOf(key), 1);
        }
      },

      changeHideTypeTitle() {
        if (this.hideTypeTitle === '详情') {
          this.hideTypeTitle = '画像';
        } else {
          this.hideTypeTitle = '详情';
        }
      },

      findIsPaidUser() {
        this.spinLoadings.push('u1-info-extend');
        this.deviceList = [];
        getAction(this.url.findIsPaidUser, {gid: this.userInfo.gid}).then((res) => {
          if (res.success) {
            this.userInfoExtend.isPay = res.message || '--';
          }
        }).finally(() => {
          this.subTimes('u1-info-extend');
        });
      },
      findLastDateByGid() {
        this.deviceList = [];
        getAction(this.url.findLastDateByGid, {gid: this.userInfo.gid}).then((res) => {
          if (res.success) {
            this.userInfoExtend.lastViewDay = res.message || '--';
          }
        }).finally(() => {
          this.subTimes();
        });
      },

      // 图谱 tab
      findDevicesByGid() {
        this.deviceList = [];
        getAction(this.url.findDevicesByGid, {gid: this.userInfo.gid}).then((res) => {
          if (res.success) {
            this.deviceList = res.result || [];
          }
        });
      },

      loadUserAccountInfoByGid(info) {
        this.spinLoadings.push('u1-info');
        getAction(this.url.findUserAccountInfoByGid, {gid: info.gid}).then((res) => {
          if (res.success) {
            this.userAccountInfo = res.result
            this.$refs.userPortrait.updateUserInfo(this.userAccountInfo);
          }
        }).finally(() => {
          this.subTimes('u1-info');
        });
      },
      getBusinessType(bus) {
        if (bus.type === 'PERSONAL') {
          return '家客'
        } else if (bus.type === 'GROUP') {
          return '集客'
        } else {
          return ''
        }
      },
      // 获取用户业务信息
      findUserBusiness(info) {
        this.spinLoadings.push('u1-business');
        getAction(this.url.findUserBusinessByGid, {gid: info.gid}).then((res) => {
          if (res.success) {
            this.userBusiness = res.result
            var createTime = this.DateDifference(this.userBusiness.createTime) + ''
            var activateTime = this.DateDifference(this.userBusiness.activateDate) + ''
            var startDate = this.DateDifference(this.userBusiness.startDate) + ''
            var time = ''
            if (createTime === 'NaN') {
              time = startDate
              if (time === 'NaN') {
                time = activateTime
              }
            } else {
              time = createTime
            }
            this.openTimeStr = time;
            this.$refs.userPortrait.updateUserInfo(this.userBusiness);
          }
        }).finally(() => {
          this.subTimes('u1-business');
        });
      },
      getPhone(phone) {
        let len = phone.length
        let result = phone;
        if (len > 3 && len < 11) {
          result = phone.substring(0, 3) + "********"
        } else if (len >= 11) {
          result = phone.substring(0, 3) + "****" + phone.substring(7, 11)
        }
        return result
      },
      getCreateTime(info) {
        var createTime = info.createTime
        var activateTime = info.activateDate
        var startDate = info.startDate
        var time = ''
        if (createTime == null) {
          time = startDate
          if (time == null) {
            time = activateTime
          }
        } else {
          time = createTime
        }
        return time;
      },
      // 获取所有标签信息
      getUserLabels(info) {
        this.spinLoadings.push('u1-tag');
        getAction(this.url.findLabelByGid, {gid: info.gid}).then((res) => {
          if (res.success) {
            this.factsLabel = res.result.factsLabel || [];
            this.modelLabel = res.result.modelLabel || [];
            this.bigDataLabel = res.result.bigDataLabel || [];
            this.$refs.userPortrait.updateWordCloud(this.factsLabel, this.modelLabel, this.bigDataLabel);
          }
        }).finally(() => {
          this.subTimes('u1-tag');
        });
      },
      // 用户特征加载状态
      featuresLoadingStatus(loadingStatus) {
        if (loadingStatus) {
          this.spinLoadings.push('u1-features');
        } else if (this.spinLoadings.includes('u1-features')) {
          this.spinLoadings.splice(this.spinLoadings.indexOf('u1-features'), 1);
        }
      },
      //获取地市名称
      // findCityNameByCityCode(info) {
      //   getAction(this.url.findCityNameByCityCode, {cityCode: info.cityCode}).then((res) => {
      //     if (res.success) {
      //       this.cityName = res.result
      //     }
      //   });
      // },
      //获取用户办理基础业务的信息详情，什么时候办理，什么时候销户等等
      findUserInfoByGid(info) {
        this.spinLoadings.push('u1-info-tab');
        getAction(this.url.findUserInfoByGid, {gid: info.gid}).then((res) => {
          if (res.success) {
            this.userAccountByTab = [];
            if (res.result.userAccountByTab) {
              let tmp = [];
              res.result.userAccountByTab.forEach(p => {
                let titles = p.split(' ');
                if (titles.length >= 2) {
                  tmp.push({
                    title: titles[1],
                    time: titles[0].replace('T', ' '),
                  });
                }
              });
              this.userAccountByTab = tmp;
              tmp = null;
            }
          }
        }).finally(() => {
          this.subTimes('u1-info-tab');
        });
      },
      DateDifference(faultDate) {
        var stime = new Date(faultDate).getTime();
        var etime = new Date().getTime();
        var usedTime = etime - stime;  //两个时间戳相差的毫秒数
        return Math.floor(usedTime / (24 * 3600 * 1000));
      },
      ...mapGetters(["nickname", "avatar"]),
      getAvatar() {
        return window._CONFIG['staticDomainURL'] + "/" + this.avatar();
      },
      getRealName(text) {
        let result = text;
        if (result !== null && typeof result !== "undefined") {
          let len = result.length;
          if (len > 1 && len < 3) {
            result = text.substring(0, 1) + "*";
          } else if (len >= 3 && len < 11) {
            result = text.substring(0, 1) + "*" + text.substring(len - 1, len);
          } else {
            result = text.substring(0, 3) + "***" + text.substring(len - 4, len);
          }
        }
        return result;
      },
      getTeams() {
        // this.$http.get('/api/workplace/teams')
        //   .then(res => {
        //     this.teams = res.result
        //     this.teamSpinning = false
        //   })
        this.teams = [{name: 'lw'}, {name: 'cj'}, {name: 'zqy'}, {name: 'shb'}]
        this.teamSpinning = false
      },
      handleTabChange(key, type) {
        this[type] = key
      },

      handleTagClose(removeTag) {
        const tags = this.tags.filter(tag => tag != removeTag)
        this.tags = tags
      },

      showTagInput() {
        this.tagInputVisible = true
        this.$nextTick(() => {
          this.$refs.tagInput.focus()
        })
      },

      handleInputChange(e) {
        this.tagInputValue = e.target.value
      },

      handleTagInputConfirm() {
        const inputValue = this.tagInputValue
        let tags = this.tags
        if (inputValue && tags.indexOf(inputValue) === -1) {
          tags = [...tags, inputValue]
        }

        Object.assign(this, {
          tags,
          tagInputVisible: false,
          tagInputValue: ''
        })
      },
      loadLabelRank() {
        getAction(this.url.labelRankUrl).then((res) => {
          if (res.success) {
            this.rankList = []
            this.rankList2 = []
            Object.keys(res.result).forEach(key => {
              this.rankList2.push({
                name: key,
                total: res.result[key]
              })
            })
            for (var i = 0; i < 7; i++) {
              this.rankList.push(this.rankList2[i])
            }
          }
        })
      },
      loadLabelCount() {
        getAction(this.url.effectiveLabelCountUrl).then((res) => {
          if (res.success) {
            this.effectiveLabelCount = res.result + ""
          }
        })
      },
      initLogInfo() {
        getLoginfo(null).then((res) => {
          if (res.success) {
            Object.keys(res.result).forEach(key => {
              res.result[key] = res.result[key] + ""
            })
            this.loginfo = res.result;
          }
        })
        getVisitInfo().then(res => {
          if (res.success) {
            this.visitInfo = res.result;
          }
        })
      },
      // 模拟数据
      loadData2(x, y, max, min, before = '', after = '点') {
        let data = []
        for (let i = 0; i < 24; i += 1) {
          data.push(
            {
              [x]: `${before}${i + 1}${after}`,
              [y]: Math.floor(Math.random() * max) + min
            }
          )
        }
        return data
      },
      // 模拟数据
      loadData(x, y, max, min, before = '', after = '点') {
        let data = []
        data.push({
          [x]: `0${after}`,
          [y]: 2
        })
        data.push({
          [x]: `1${after}`,
          [y]: 1
        })
        data.push({
          [x]: `2${after}`,
          [y]: 0
        })
        data.push({
          [x]: `3${after}`,
          [y]: 0
        })
        data.push({
          [x]: `4${after}`,
          [y]: 0
        })
        data.push({
          [x]: `5${after}`,
          [y]: 0
        })
        data.push({
          [x]: `6${after}`,
          [y]: 0
        })
        data.push({
          [x]: `7${after}`,
          [y]: 0
        })
        data.push({
          [x]: `8${after}`,
          [y]: 0
        })
        data.push({
          [x]: `9${after}`,
          [y]: 2
        })
        data.push({
          [x]: `10${after}`,
          [y]: 2
        })
        data.push({
          [x]: `11${after}`,
          [y]: 2
        })
        data.push({
          [x]: `12${after}`,
          [y]: 3
        })
        data.push({
          [x]: `13${after}`,
          [y]: 5
        })
        data.push({
          [x]: `14${after}`,
          [y]: 6
        })
        data.push({
          [x]: `15${after}`,
          [y]: 6
        })
        data.push({
          [x]: `16${after}`,
          [y]: 5
        })
        data.push({
          [x]: `17${after}`,
          [y]: 5
        })
        data.push({
          [x]: `18${after}`,
          [y]: 5
        })
        data.push({
          [x]: `19${after}`,
          [y]: 15
        })
        data.push({
          [x]: `20${after}`,
          [y]: 25
        })
        data.push({
          [x]: `21${after}`,
          [y]: 10
        })
        data.push({
          [x]: `22${after}`,
          [y]: 5
        })
        data.push({
          [x]: `23${after}`,
          [y]: 5
        })
        data.push({
          [x]: `24${after}`,
          [y]: 3
        })
        return data
      },
      // 活跃用户数
      loadActiveUserData() {
        this.activeUserData = this.loadData('x', 'y', 10000, 100000)
      },
      // 年龄层分布
      loadAverageUserNumData() {
        this.averageAgeUserNum = this.loadData2('x', 'y', 10000, 100000)
      },
      // 各生命周期用户数排行
      loadPieRankList() {
        this.pieRankList = [
          {name: `成熟期`, total: 376232},
          {name: `成长期`, total: 210000},
          {name: `新客期`, total: 203434},
          {name: `衰退期`, total: 131231},
          {name: `流失期`, total: 98212},
        ]
      }
    },
    watch: {
      $route(to, from) {
        if (to.path.startsWith('/label/UserProfileInfo/') && to.query.reload && to.params.gid === this.userInfo.gid) {
          this.initData();
        }
      },
      'rightContentWidth': 'handleWidthChange'
    }
  }
</script>

<style lang="less">
  .user-profile-info {
    .right-info {
      .timeline-content {
        p {
          margin-bottom: 0;
        }
      }
    }
  }

  .clear::after {
    content: "";
    display: block;
    clear: both;
  }

  .upiUlBtnDefault {
    position: absolute;
    left: 6px;
    top: 6px;
    z-index: 1;
  }

  .upiUlBtnDefault.upi-ul-btn- {
    left: -12px;

    span {
      display: none;
    }

    &:hover {
      span {
        display: inline-block;
      }
    }
  }
</style>

<style lang="less" scoped>
  .user-profile-info {
    background-color: #f0f2f5;
    position: relative;
    padding: 4px 20px;

    .right-info {
      margin-left: 12px;

      .head-overall, .head-overall-chart {
        text-align: center;
        font-size: 0;
        padding: 0;
        height: 62px;
        display: flex;

        li {
          width: 100%;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-orient: vertical;
          -webkit-box-direction: normal;
          -ms-flex-direction: column;
          flex-direction: column;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          position: relative;

          .num {
            color: #222226;
            font-family: DINCond-Black;
            font-size: 24px;
            line-height: 24px;
            margin-bottom: 16px;
          }

          .name {
            color: #999aaa;
            font-size: 14px;
            line-height: 16px;
          }

          &:after {
            position: absolute;
            right: 0;
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            content: "";
            height: 24px;
            width: 1px;
            background: #e8e8ed;
          }

          &:last-child:after {
            display: none
          }

        }
      }

      .head-overall-chart {
        height: 84px;

        li {

          .num {
            margin-bottom: 0px;
          }

          .name {
            color: #222226;
          }

          &:after {
            display: none;
          }

        }
      }

      .order-overall {
        span {
          display: block;
          float: left;
          height: 22px;
        }

        a {
          display: block;
          margin-left: 140px;
          height: 28px;
          padding-top: 4px;
        }
      }


      .timeline-content {
        background-color: rgba(220, 220, 220, 0.3);
        /*line-height: 60px;*/
        border-radius: 20px;
        margin: 0px 12px;
        padding: 20px 14px;

        p {
          margin-bottom: 0;
        }
      }
    }

  }

  .userTopImg {
    /*background: url('/userTopImage.jpg');*/
    width: 100%;
    /*height: 100px;*/
    /*background-size: 100% 100px; //火狐浏览器*/
    position: absolute;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='userTopImage.jpg', sizingMethod='scale'); //ie浏览器
  }

  .userTopImg2 {
    /*background: url('/userTopImage2.jpeg');*/
    width: 100%;
    /*height: 100px;*/
    /*background-size: 100% 100px; //火狐浏览器*/
    position: absolute;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='userTopImage2.jpeg', sizingMethod='scale'); //ie浏览器
  }

  .userTopImg3 {
    /*background: url('/userTopImage3.gif');*/
    width: 100%;
    /*height: 100px;*/
    /*background-size: 100% 100px; //火狐浏览器*/
    position: absolute;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='userTopImage3.gif', sizingMethod='scale'); //ie浏览器
  }

  .userTopImg4 {
    /*background: url('/userTopImage4.jpg');*/
    width: 100%;
    /*height: 100px;*/
    /*background-size: 100% 100px; //火狐浏览器*/
    position: absolute;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='userTopImage4.jpg', sizingMethod='scale'); //ie浏览器
  }

  .circle-cust {
    position: relative;
    top: 28px;
    left: -100%;
  }

  .extra-wrapper {
    line-height: 55px;
    padding-right: 24px;

    .extra-item {
      display: inline-block;
      margin-right: 24px;

      a {
        margin-left: 24px;
      }
    }
  }

  /* 首页访问量统计 */
  .head-info {
    position: relative;
    text-align: left;
    padding: 0 32px 0 0;
    min-width: 125px;

    &.center {
      text-align: center;
      padding: 0 32px;
    }

    span {
      color: rgba(0, 0, 0, .45);
      display: inline-block;
      font-size: .95rem;
      line-height: 42px;
      margin-bottom: 4px;
    }

    p {
      line-height: 42px;
      margin: 0;

      a {
        font-weight: 600;
        font-size: 1rem;
      }
    }
  }

  .account-center-avatarHolder {
    /*text-align: center;*/
    margin-bottom: 24px;

    & > .avatar {
      margin: 0 auto;
      width: 104px;
      height: 104px;
      margin-bottom: 20px;
      border-radius: 50%;
      overflow: hidden;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .info {
      margin-left: 24px;
      display: inline-block;
      vertical-align: middle;

      .username {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 12px;
      }

      .gid {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 1px;
      }
    }
  }

  .page-header-index-wide {
    .ivu-pl-8 {
      font-size: 14px;
      padding-left: 8px !important;
    }

    .right-info {
      .salesCard {
        .tab-inner-title {
          margin-bottom: 30px;
          margin-top: -36px;
          margin-left: 28px;

          span {
            display: inline-block;
            background-color: white;
            padding: 0 10px;

            &::before {
              content: '';
              width: 4px;
              height: 12px;
              display: inline-block;
              background-color: var(--theme);
              margin-right: 10px;
            }
          }
        }
      }
    }
  }

  .account-center-tags {
    padding-right: 24px;
    overflow: auto;
    max-height: 240px;

    .ant-tag {
      margin-bottom: 8px;
    }
  }

  .tagsTitle, .teamTitle {
    font-weight: 500;
    color: rgba(0, 0, 0, .85);
    margin-bottom: 8px;
    text-align: center;
  }

  .label-model {
    overflow-y: auto;
    overflow-x: hidden
  }

  .account-center-detail {

    p {
      margin-bottom: 8px;
      padding-left: 26px;
      position: relative;
    }

    i {
      position: absolute;
      height: 14px;
      width: 14px;
      left: 0;
      top: 4px;
    }

    .title {
      background-position: 0 0;
    }

    .group {
      background-position: 0 -22px;
    }

    .address {
      background-position: 0 -44px;
    }
  }

  .text-shenglue {
    display: block;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    -icab-text-overflow: ellipsis;
    -khtml-text-overflow: ellipsis;
    -moz-text-overflow: ellipsis;
    -webkit-text-overflow: ellipsis;
  }

  .seal-cancel {
    position: absolute;
    top: 43px;
    right: -3px;
    border: 2px dashed;
    color: #f40;
    display: block;
    width: 140px;
    height: 42px;
    line-height: 34px;
    font-size: 24px;
    z-index: 100;
    letter-spacing: 12px;
    padding-left: 12px;
    transform: rotate(43deg);
    cursor: default;
  }

  .gray {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    //-ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    //filter: gray;
  }

</style>
